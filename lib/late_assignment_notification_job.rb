# frozen_string_literal: true

require_dependency 'assignment_notification_helper'

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.

require_dependency 'auditors/assignment'

class LateAssignmentNotificationJob
  # Checks for assignments that have recently become late
  # and sends notifications to students and teachers
  # Only sends notifications once per assignment per user
  def self.process
    # Find assignments that have become overdue in the past hour
    # This ensures we only notify about assignments that have recently become late
    recently_due_assignments = Assignment.active.select(&:overdue?)
    
    recently_due_assignments.each do |assignment|
      process_late_assignment(assignment)
    end
  end

  def self.process_late_assignment(assignment)
    course = assignment.course
    return unless course&.available?

    # Get all students who should be notified
    students = course.participating_students_by_date
    teachers = course.participating_instructors_by_date
    
    # For each student, check if they've submitted the assignment
    students.each do |student|    
      # Skip if student has already submitted
      next if assignment.submissions.where(user_id: student.id).having_submission.exists?
      
      # Skip if we've already sent a notification to this student for this assignment
      next if LateAssignmentNotification.already_sent?(assignment.id, student.id, LateAssignmentNotification::LATE)
            
      # Send notification to student
      if send_late_notification_to_student(student, assignment, course)
        # Mark notification as sent      
        LateAssignmentNotification.mark_as_sent(assignment.id, student.id, LateAssignmentNotification::LATE)
      end
    end

    # Notify teachers about all late assignments
    teachers.each do |teacher|
      # Skip if we've already sent a notification to this teacher for this assignment
      next if LateAssignmentNotification.already_sent?(assignment.id, teacher.id, LateAssignmentNotification::LATE)
      
      # Send notification to teacher
      if send_late_notification_to_teacher(teacher, assignment, course)
        # Mark notification as sent
        LateAssignmentNotification.mark_as_sent(assignment.id, teacher.id, LateAssignmentNotification::LATE)
      end
    end
  end

  def self.send_late_notification_to_student(student, assignment, course)
    return false unless student.email_channel

    # Create a message for the student
    subject = I18n.t("Late Assignment Notification: %{assignment_title}", assignment_title: assignment.title)
    body = I18n.t(<<~MESSAGE, 
      Dear %{student_name},

      This is a reminder that the assignment "%{assignment_title}" in the course "%{course_name}" is now late.
      The assignment was due on %{due_date}.
      
      Please submit your work as soon as possible to minimize any late penalties.
      
      You can access the assignment here: %{assignment_url}
      
      This is an automated message. Please do not reply to this email.
    MESSAGE
    student_name: student.name,
    assignment_title: assignment.title,
    course_name: course.name,
    due_date: format_date(assignment.due_at),
    assignment_url: assignment_url(assignment, course))

    # Return true if the email was sent successfully
    send_notification_email(student, subject, body, course, assignment, 'late_assignment_notification_to_student')
  end

  def self.send_late_notification_to_teacher(teacher, assignment, course)
    return false unless teacher.email_channel

    # Get count of missing submissions
    total_students = course.participating_students_by_date.count
    submitted_count = assignment.submissions.having_submission.count
    missing_count = total_students - submitted_count
    
    return false if missing_count <= 0

    # Create a message for the teacher
    subject = I18n.t("Late Submissions Alert: %{assignment_title}", assignment_title: assignment.title)
    body = I18n.t(<<~MESSAGE,
      Dear %{teacher_name},

      This is to inform you that the assignment "%{assignment_title}" in your course "%{course_name}" is now past due.
      
      Due date: %{due_date}
      Missing submissions: %{missing_count} out of %{total_students} students
      
      You can review submissions here: %{speedgrader_url}
      
      This is an automated message. Please do not reply to this email.
    MESSAGE
    teacher_name: teacher.name,
    assignment_title: assignment.title,
    course_name: course.name,
    due_date: format_date(assignment.due_at),
    missing_count: missing_count,
    total_students: total_students,
    speedgrader_url: speedgrader_url(assignment, course))

    # Return true if the email was sent successfully
    send_notification_email(teacher, subject, body, course, assignment, 'late_assignment_notification_to_teacher')
  end

  def self.send_notification_email(user, subject, body, context, assignment, action)
    AssignmentNotificationHelper.send_notification_email(
      user, 
      subject, 
      body, 
      context, 
      assignment, 
      action
    )
  end

  def self.format_date(date)
    # Use the same format as defined in the memory for consistency
    I18n.l(date, format: :long_date_at_time)
  end

  def self.assignment_url(assignment, course)
    "#{HostUrl.protocol}://#{HostUrl.default_host}/courses/#{course.id}/assignments/#{assignment.id}"
  end

  def self.speedgrader_url(assignment, course)
    "#{HostUrl.protocol}://#{HostUrl.default_host}/courses/#{course.id}/gradebook/speed_grader?assignment_id=#{assignment.id}"
  end
end
