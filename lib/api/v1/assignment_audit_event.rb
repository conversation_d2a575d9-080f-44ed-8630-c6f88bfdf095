# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

module Api::V1::AssignmentAuditEvent
  include Api::V1::Json
  include Api::V1::Assignment
  include Api::V1::Course
  include Api::V1::User

  def assignment_audit_event_json(event, _user, _session)
    {
      id: event.id,
      created_at: event.created_at.iso8601,
      event_type: event.event_type,
      assignment_id: event.assignment_id,
      user_id: event.user_id
    }
  end

  def assignment_audit_events_compound_json(events, user, session)
    events_json = events.map { |event| assignment_audit_event_json(event, user, session) }

    # Collect all the referenced objects
    assignments = {}
    courses = {}
    users = {}

    events.each do |event|
      assignment = event.assignment
      assignments[assignment.id] = assignment if assignment
      
      course = event.course
      courses[course.id] = course if course
      
      actor = event.user
      users[actor.id] = actor if actor
    end

    # Format all the referenced objects
    linked = {
      assignments: assignments.values.map { |a| assignment_json(a, user, session) },
      courses: courses.values.map { |c| course_json(c, user, session, [], nil) },
      users: users.values.map { |u| user_json(u, user, session) }
    }

    { events: events_json, linked: linked }
  end
end
