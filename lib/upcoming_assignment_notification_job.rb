# frozen_string_literal: true

require_dependency 'assignment_notification_helper'
require_dependency 'auditors/assignment'

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.



class UpcomingAssignmentNotificationJob
  # Checks for upcoming assignments and sends reminder notifications to students
  # For assignments due in 3 days or less, if a notification hasn't been sent yet
  # Only sends notifications once per assignment per user
  def self.process
    now = Time.now.utc
    # Find assignments due between now and 3 days from now
    min_time = now
    max_time = 3.days.from_now.end_of_day
    
    # Find all upcoming assignments that are due within the next 3 days
    upcoming_assignments = Assignment.active
                                    .where('due_at > ? AND due_at <= ?', min_time, max_time)
                                    .where.not(due_at: nil)
    
    upcoming_assignments.each do |assignment|
      process_upcoming_assignment(assignment)
    end
  end

  def self.process_upcoming_assignment(assignment)
    course = assignment.course
    return unless course&.available?

    # Get all students who should be notified
    students = course.participating_students_by_date
    teachers = course.participating_instructors_by_date

    # For each student, send a reminder notification
    students.each do |student|
      # Skip if we've already sent a notification to this student for this assignment
      next if LateAssignmentNotification.already_sent?(assignment.id, student.id, LateAssignmentNotification::UPCOMING)
      
      # Send notification to student
      if send_upcoming_notification_to_student(student, assignment, course)
        # Mark notification as sent
        LateAssignmentNotification.mark_as_sent(assignment.id, student.id, LateAssignmentNotification::UPCOMING)
      end
    end

    # Notify teachers about all assignments
    teachers.each do |teacher|
      # Skip if we've already sent a notification to this teacher for this assignment
      next if LateAssignmentNotification.already_sent?(assignment.id, teacher.id, LateAssignmentNotification::UPCOMING)
      
      # Send notification to teacher
      if send_upcoming_notification_to_teacher(teacher, assignment, course)
        # Mark notification as sent
        LateAssignmentNotification.mark_as_sent(assignment.id, teacher.id, LateAssignmentNotification::UPCOMING)
      end
    end
  end

  def self.send_upcoming_notification_to_student(student, assignment, course)
    return false unless student.email_channel

    # Calculate days until due
    days_until_due = ((assignment.due_at - Time.now.utc) / 1.day).ceil
    time_message = if days_until_due <= 0
                     "today"
                   elsif days_until_due == 1
                     "tomorrow"
                   else
                     "in #{days_until_due} days"
                   end

    # Create a message for the student
    subject = I18n.t("Upcoming Assignment Reminder: %{assignment_title}", assignment_title: assignment.title)
    body = I18n.t(<<~MESSAGE, 
      Dear %{student_name},

      This is a reminder that the assignment "%{assignment_title}" in the course "%{course_name}" is due #{time_message}.
      The assignment is due on %{due_date}.
      
      Please plan accordingly to submit your work on time.
      
      You can access the assignment here: %{assignment_url}
      
      This is an automated message. Please do not reply to this email.
    MESSAGE
    student_name: student.name,
    assignment_title: assignment.title,
    course_name: course.name,
    due_date: format_date(assignment.due_at),
    assignment_url: assignment_url(assignment, course))

    # Return true if the email was sent successfully
    send_notification_email(student, subject, body, course, 'upcoming_assignment_notification_to_student', assignment)
  end
  
  def self.send_upcoming_notification_to_teacher(teacher, assignment, course)
    return false unless teacher.email_channel

    # Calculate days until due
    days_until_due = ((assignment.due_at - Time.now.utc) / 1.day).ceil
    time_message = if days_until_due <= 0
                     "today"
                   elsif days_until_due == 1
                     "tomorrow"
                   else
                     "in #{days_until_due} days"
                   end

    # Create a message for the teacher
    subject = I18n.t("Upcoming Assignment Alert: %{assignment_title}", assignment_title: assignment.title)
    body = I18n.t(<<~MESSAGE, 
      Dear %{teacher_name},

      This is a notification that the assignment "%{assignment_title}" in your course "%{course_name}" is due #{time_message}.
      The assignment is due on %{due_date}.
      
      Students have been notified of this upcoming deadline.
      
      You can access the assignment here: %{assignment_url}
      You can access the SpeedGrader here: %{speedgrader_url}
      
      This is an automated message. Please do not reply to this email.
    MESSAGE
    teacher_name: teacher.name,
    assignment_title: assignment.title,
    course_name: course.name,
    due_date: format_date(assignment.due_at),
    assignment_url: assignment_url(assignment, course),
    speedgrader_url: speedgrader_url(assignment, course))

    # Return true if the email was sent successfully
    send_notification_email(teacher, subject, body, course, 'upcoming_assignment_notification_to_teacher', assignment)
  end

  def self.send_notification_email(user, subject, body, context, action, assignment)
    AssignmentNotificationHelper.send_notification_email(
      user,
      subject,
      body,
      context,
      assignment,
      action,
      LateAssignmentNotification::UPCOMING
    )
  end

  def self.format_date(date)
    AssignmentNotificationHelper.format_date(date)
  end

  def self.assignment_url(assignment, course)
    AssignmentNotificationHelper.assignment_url(assignment, course)
  end
  
  def self.speedgrader_url(assignment, course)
    AssignmentNotificationHelper.speedgrader_url(assignment, course)
  end
end
