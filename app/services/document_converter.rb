# frozen_string_literal: true

require 'mini_magick'
require 'base64'

class DocumentConverter
  class ConversionError < StandardError; end

  def self.convert_to_pdf(file_path, output_dir = nil)
    output_dir ||= File.dirname(file_path)
    output_path = File.join(output_dir, "#{File.basename(file_path, '.*')}.pdf")

    # Clean up any existing converted file
    File.delete(output_path) if File.exist?(output_path)

    # Check if LibreOffice is available
    if system("which libreoffice > /dev/null 2>&1")
      # Convert using LibreOffice
      cmd = "libreoffice --headless --convert-to pdf --outdir \"#{output_dir}\" \"#{file_path}\""
      Rails.logger.info("Running command: #{cmd}")

      success = system(cmd)

      if success && File.exist?(output_path)
        return output_path
      else
        Rails.logger.error("Failed to convert document: #{file_path}")
        raise ConversionError, "Failed to convert document to PDF"
      end
    else
      # LibreOffice not available - create a placeholder PDF for testing
      Rails.logger.warn("LibreOffice not available, creating placeholder PDF")
      create_placeholder_pdf(file_path, output_path)
      return output_path
    end
  end

  def self.convert_to_image(file_path, output_dir = nil, format = 'png')
    output_dir ||= File.dirname(file_path)
    base_name = File.basename(file_path, '.*')

    begin
      # First try to convert directly if it's already a PDF
      if File.extname(file_path).downcase == '.pdf'
        image_paths = convert_pdf_to_images(file_path, output_dir, format)
      else
        # For office documents, convert to PDF first, then to images
        temp_pdf_path = convert_to_pdf(file_path, output_dir)
        image_paths = convert_pdf_to_images(temp_pdf_path, output_dir, format)

        # Clean up temporary PDF
        File.delete(temp_pdf_path) if temp_pdf_path && File.exist?(temp_pdf_path) && temp_pdf_path != file_path
      end

      if image_paths && image_paths.any? { |path| File.exist?(path) }
        return image_paths
      else
        raise ConversionError, "Failed to convert document to images"
      end
    rescue => e
      Rails.logger.error("Failed to convert document to images: #{e.message}")
      raise ConversionError, "Failed to convert document to images: #{e.message}"
    end
  end

  def self.convert_pdf_to_images(pdf_path, output_dir, format = 'png')
    # Convert all pages of PDF to separate image files
    begin
      # Check if the PDF file exists
      unless File.exist?(pdf_path)
        Rails.logger.error("PDF file does not exist: #{pdf_path}")
        placeholder_path = File.join(output_dir, "page_001.#{format}")
        create_placeholder_image(pdf_path, placeholder_path, format)
        return [placeholder_path]
      end

      Rails.logger.info("Converting PDF to images - PDF: #{pdf_path}, Output dir: #{output_dir}")
      Rails.logger.info("PDF file size: #{File.size(pdf_path)} bytes")

      base_name = File.basename(pdf_path, '.*')
      image_paths = []

      # Check if ImageMagick is available
      if system("which convert > /dev/null 2>&1")
        # Use ImageMagick to convert all pages
        # The output pattern will create page_001.png, page_002.png, etc.
        output_pattern = File.join(output_dir, "page_%03d.#{format}")
        cmd = "convert \"#{pdf_path}\" -density 150 -quality 90 -background white -alpha remove \"#{output_pattern}\""
        Rails.logger.info("Running ImageMagick command: #{cmd}")

        success = system(cmd)

        if success
          # Find all generated image files
          Dir.glob(File.join(output_dir, "page_*.#{format}")).sort.each do |img_path|
            if File.exist?(img_path) && File.size(img_path) > 0
              image_paths << img_path
              Rails.logger.info("Created page image: #{img_path} (#{File.size(img_path)} bytes)")
            end
          end
        end

        if image_paths.empty?
          Rails.logger.error("ImageMagick conversion failed - no images created")
          # Try with MiniMagick as fallback
          image_paths = try_minimagick_multi_page_conversion(pdf_path, output_dir, format)
        end
      else
        Rails.logger.warn("ImageMagick not available, trying MiniMagick")
        image_paths = try_minimagick_multi_page_conversion(pdf_path, output_dir, format)
      end

      # If no images were created, create a placeholder
      if image_paths.empty?
        placeholder_path = File.join(output_dir, "page_001.#{format}")
        create_placeholder_image(pdf_path, placeholder_path, format)
        image_paths = [placeholder_path]
      end

      Rails.logger.info("Successfully converted PDF to #{image_paths.length} images")
      return image_paths

    rescue => e
      Rails.logger.error("Error in PDF to images conversion: #{e.message}")
      Rails.logger.error("Backtrace: #{e.backtrace.join("\n")}")
      # Fallback: create a placeholder image
      placeholder_path = File.join(output_dir, "page_001.#{format}")
      create_placeholder_image(pdf_path, placeholder_path, format)
      return [placeholder_path]
    end
  end

  def self.convert_pdf_to_image(pdf_path, output_path, format = 'png')
    # Use ImageMagick/MiniMagick to convert PDF to image
    # Take only the first page for preview
    begin
      # Check if the PDF file exists
      unless File.exist?(pdf_path)
        Rails.logger.error("PDF file does not exist: #{pdf_path}")
        create_placeholder_image(pdf_path, output_path, format)
        return
      end

      Rails.logger.info("Converting PDF to image - PDF: #{pdf_path}, Output: #{output_path}")
      Rails.logger.info("PDF file size: #{File.size(pdf_path)} bytes")

      # Check if ImageMagick is available
      if system("which convert > /dev/null 2>&1")
        # Use ImageMagick convert command directly for better control
        # Convert only the first page of the PDF
        cmd = "convert \"#{pdf_path}[0]\" -density 150 -quality 90 -background white -alpha remove \"#{output_path}\""
        Rails.logger.info("Running ImageMagick command: #{cmd}")

        success = system(cmd)

        if success && File.exist?(output_path)
          Rails.logger.info("Successfully converted PDF to image: #{output_path}")
          Rails.logger.info("Output image size: #{File.size(output_path)} bytes")
        else
          Rails.logger.error("ImageMagick conversion failed or output file not created")
          # Try with MiniMagick as fallback
          try_minimagick_conversion(pdf_path, output_path, format)
        end
      else
        Rails.logger.warn("ImageMagick not available, trying MiniMagick")
        try_minimagick_conversion(pdf_path, output_path, format)
      end

    rescue => e
      Rails.logger.error("Error in PDF to image conversion: #{e.message}")
      Rails.logger.error("Backtrace: #{e.backtrace.join("\n")}")
      # Fallback: create a placeholder image
      create_placeholder_image(pdf_path, output_path, format)
    end
  end

  def self.try_minimagick_multi_page_conversion(pdf_path, output_dir, format = 'png')
    begin
      Rails.logger.info("Trying MiniMagick multi-page conversion as fallback")

      # Check if we can use MiniMagick (requires ImageMagick to be installed)
      begin
        MiniMagick.validate!
      rescue MiniMagick::Error => e
        Rails.logger.error("MiniMagick validation failed: #{e.message}")
        return []
      end

      image_paths = []

      # Try to determine the number of pages in the PDF
      begin
        # Open the PDF to get page count
        pdf_image = MiniMagick::Image.open(pdf_path)
        page_count = pdf_image.pages.length
        Rails.logger.info("PDF has #{page_count} pages")

        # Convert each page
        (0...page_count).each do |page_index|
          begin
            page_image = MiniMagick::Image.open("#{pdf_path}[#{page_index}]")
            page_image.format(format)
            page_image.density(150)
            page_image.quality(90)
            page_image.background('white')
            page_image.alpha('remove')

            output_path = File.join(output_dir, "page_%03d.#{format}" % (page_index + 1))
            page_image.write(output_path)

            if File.exist?(output_path)
              image_paths << output_path
              Rails.logger.info("Created page #{page_index + 1}: #{output_path}")
            end
          rescue => page_error
            Rails.logger.error("Failed to convert page #{page_index + 1}: #{page_error.message}")
          end
        end

      rescue => e
        Rails.logger.error("Failed to get PDF page count or convert pages: #{e.message}")
        # Try to convert at least the first page
        begin
          first_page = MiniMagick::Image.open("#{pdf_path}[0]")
          first_page.format(format)
          first_page.density(150)
          first_page.quality(90)
          first_page.background('white')
          first_page.alpha('remove')

          output_path = File.join(output_dir, "page_001.#{format}")
          first_page.write(output_path)

          if File.exist?(output_path)
            image_paths << output_path
            Rails.logger.info("Created first page only: #{output_path}")
          end
        rescue => first_page_error
          Rails.logger.error("Failed to convert even the first page: #{first_page_error.message}")
        end
      end

      return image_paths

    rescue MiniMagick::Error => e
      Rails.logger.error("MiniMagick multi-page conversion failed: #{e.message}")
      return []
    rescue => e
      Rails.logger.error("General error in MiniMagick multi-page conversion: #{e.message}")
      Rails.logger.error("Error class: #{e.class}")
      return []
    end
  end

  def self.try_minimagick_conversion(pdf_path, output_path, format = 'png')
    begin
      Rails.logger.info("Trying MiniMagick conversion as fallback")

      # Check if we can use MiniMagick (requires ImageMagick to be installed)
      begin
        MiniMagick.validate!
      rescue MiniMagick::Error => e
        Rails.logger.error("MiniMagick validation failed: #{e.message}")
        create_placeholder_image(pdf_path, output_path, format)
        return
      end

      # Try to open the PDF with MiniMagick
      # For PDFs, we need to specify the first page
      if File.extname(pdf_path).downcase == '.pdf'
        # Try different approaches to get the first page
        begin
          # Method 1: Use the [0] syntax
          image = MiniMagick::Image.open("#{pdf_path}[0]")
        rescue
          # Method 2: Open normally and try to get first page
          image = MiniMagick::Image.open(pdf_path)
          # If it's a multi-page PDF, this might still work
        end
      else
        image = MiniMagick::Image.open(pdf_path)
      end

      image.format(format)
      image.density(150)
      image.quality(90)
      image.background('white')
      image.alpha('remove')
      image.write(output_path)

      if File.exist?(output_path)
        Rails.logger.info("MiniMagick conversion successful: #{output_path}")
      else
        Rails.logger.error("MiniMagick conversion failed - output file not created")
        create_placeholder_image(pdf_path, output_path, format)
      end

    rescue MiniMagick::Error => e
      Rails.logger.error("MiniMagick conversion failed: #{e.message}")
      create_placeholder_image(pdf_path, output_path, format)
    rescue => e
      Rails.logger.error("General error in MiniMagick conversion: #{e.message}")
      Rails.logger.error("Error class: #{e.class}")
      create_placeholder_image(pdf_path, output_path, format)
    end
  end

  private

  def self.create_placeholder_pdf(original_file, output_path)
    # Create a simple PDF with basic content indicating the original file
    original_filename = File.basename(original_file)

    # Simple PDF content (this is a minimal PDF structure)
    pdf_content = <<~PDF
      %PDF-1.4
      1 0 obj
      <<
      /Type /Catalog
      /Pages 2 0 R
      >>
      endobj

      2 0 obj
      <<
      /Type /Pages
      /Kids [3 0 R]
      /Count 1
      >>
      endobj

      3 0 obj
      <<
      /Type /Page
      /Parent 2 0 R
      /MediaBox [0 0 612 792]
      /Contents 4 0 R
      /Resources <<
        /Font <<
          /F1 5 0 R
        >>
      >>
      >>
      endobj

      4 0 obj
      <<
      /Length 120
      >>
      stream
      BT
      /F1 12 Tf
      50 750 Td
      (Document Preview) Tj
      0 -20 Td
      (Original file: #{original_filename}) Tj
      0 -20 Td
      (LibreOffice conversion not available) Tj
      ET
      endstream
      endobj

      5 0 obj
      <<
      /Type /Font
      /Subtype /Type1
      /BaseFont /Helvetica
      >>
      endobj

      xref
      0 6
      0000000000 65535 f
      0000000009 00000 n
      0000000058 00000 n
      0000000115 00000 n
      0000000274 00000 n
      0000000445 00000 n
      trailer
      <<
      /Size 6
      /Root 1 0 R
      >>
      startxref
      522
      %%EOF
    PDF

    File.write(output_path, pdf_content)
    Rails.logger.info("Created placeholder PDF: #{output_path}")
  end

  def self.create_placeholder_image(original_file, output_path, format = 'png')
    # Create a simple placeholder image when conversion fails
    original_filename = File.basename(original_file)

    Rails.logger.info("Creating placeholder image for: #{original_filename}")

    begin
      # Try to create a placeholder with MiniMagick if available
      if system("which convert > /dev/null 2>&1")
        # Use ImageMagick directly to create a simple placeholder
        text = "Document Preview\\n\\nFile: #{original_filename}\\n\\nPreview not available"
        cmd = "convert -size 800x600 xc:lightgray -gravity center -pointsize 20 -fill black -annotate +0+0 \"#{text}\" \"#{output_path}\""

        success = system(cmd)
        if success && File.exist?(output_path)
          Rails.logger.info("Created placeholder image with ImageMagick: #{output_path}")
          return
        end
      end

      # Fallback: try with MiniMagick
      begin
        # Create a simple colored background
        image = MiniMagick::Image.open("xc:lightgray[800x600]")
        image.format(format)
        image.write(output_path)
        Rails.logger.info("Created simple placeholder image: #{output_path}")
      rescue => e
        Rails.logger.error("MiniMagick placeholder creation failed: #{e.message}")
        # Last resort: create a minimal image file
        create_minimal_placeholder_image(output_path, format)
      end

    rescue => e
      Rails.logger.error("Failed to create placeholder image: #{e.message}")
      # Last resort: create a minimal image file
      create_minimal_placeholder_image(output_path, format)
    end
  end

  def self.create_minimal_placeholder_image(output_path, format = 'png')
    # Create the most basic placeholder image possible
    Rails.logger.info("Creating minimal placeholder image")

    begin
      # Try with ImageMagick command line first
      if system("which convert > /dev/null 2>&1")
        cmd = "convert -size 400x300 xc:lightgray \"#{output_path}\""
        success = system(cmd)
        if success && File.exist?(output_path)
          Rails.logger.info("Created minimal placeholder with ImageMagick: #{output_path}")
          return
        end
      end

      # Try with MiniMagick
      image = MiniMagick::Image.open("xc:lightgray[400x300]")
      image.format(format)
      image.write(output_path)
      Rails.logger.info("Created minimal placeholder image with MiniMagick: #{output_path}")

    rescue => e
      Rails.logger.error("Failed to create minimal placeholder image: #{e.message}")

      # Last resort: create a simple PNG file manually
      begin
        create_basic_png_file(output_path)
      rescue => png_error
        Rails.logger.error("Failed to create basic PNG file: #{png_error.message}")
        raise ConversionError, "Unable to create any placeholder image: #{e.message}"
      end
    end
  end

  def self.create_basic_png_file(output_path)
    # Create a minimal valid PNG file (1x1 pixel gray)
    # This is a base64 encoded 1x1 gray PNG
    png_data = Base64.decode64("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==")
    File.binwrite(output_path, png_data)
    Rails.logger.info("Created basic PNG file: #{output_path}")
  end

  def self.temp_file(extension, content = nil)
    temp_file = Tempfile.new(['document', ".#{extension}"])
    temp_file.binmode
    
    if content
      temp_file.write(content)
      temp_file.rewind
    end
    
    yield temp_file
  ensure
    temp_file.close
    temp_file.unlink
  end
end
