# Custom Media Service to replace Kaltura functionality
class CustomMediaService
  include Rails.application.routes.url_helpers

  # Supported media types
  SUPPORTED_VIDEO_TYPES = %w[mp4 webm ogg mov avi mkv].freeze
  SUPPORTED_AUDIO_TYPES = %w[mp3 wav ogg m4a aac].freeze
  SUPPORTED_TYPES = (SUPPORTED_VIDEO_TYPES + SUPPORTED_AUDIO_TYPES).freeze

  class << self
    # Check if a media file exists and is accessible
    def media_exists?(media_id)
      media_object = MediaObject.by_media_id(media_id).first
      return false unless media_object

      attachment = media_object.attachment
      return false unless attachment

      # Check if the attachment exists and is not deleted
      # For development/testing, we don't require the physical file to exist
      attachment.present? && !attachment.deleted?
    rescue
      false
    end

    # Get media sources for a media object
    def media_sources(media_object)
      return [] unless media_object&.attachment

      attachment = media_object.attachment
      return [] unless attachment&.content_type&.start_with?('video/', 'audio/')

      file_extension = File.extname(attachment.filename).delete('.').downcase
      return [] unless SUPPORTED_TYPES.include?(file_extension)

      [{
        url: media_download_url(attachment),
        content_type: attachment.content_type,
        file_extension: file_extension,
        size: attachment.size,
        width: extract_width(attachment),
        height: extract_height(attachment),
        duration: extract_duration(attachment),
        bitrate: extract_bitrate(attachment)
      }]
    end

    # Generate thumbnail URL for video files
    def thumbnail_url(media_object, options = {})
      return nil unless media_object&.attachment

      attachment = media_object.attachment
      return nil unless attachment&.content_type&.start_with?('video/')

      # Generate thumbnail using attachment ID and options
      begin
        Rails.application.routes.url_helpers.media_object_thumbnail_url(
          media_object.media_id,
          width: options[:width] || 140,
          height: options[:height] || 100,
          time: options[:time] || 5
        )
      rescue ArgumentError => e
        # Handle missing host error when running outside web context
        if e.message.include?("Missing host")
          "/media_objects/#{media_object.media_id}/thumbnail?width=#{options[:width] || 140}&height=#{options[:height] || 100}&time=#{options[:time] || 5}"
        else
          raise e
        end
      end
    end

    # Extract media metadata
    def extract_metadata(attachment)
      return {} unless attachment

      metadata = {
        filename: attachment.display_name,
        size: attachment.size,
        content_type: attachment.content_type,
        duration: nil,
        width: nil,
        height: nil,
        bitrate: nil
      }

      # Try to extract additional metadata if ffmpeg is available
      if ffmpeg_available? && attachment.content_type&.start_with?('video/', 'audio/')
        ffmpeg_metadata = extract_ffmpeg_metadata(attachment)
        metadata.merge!(ffmpeg_metadata)
      end

      metadata
    end

    # Create media object from attachment
    def create_media_object_from_attachment(attachment, options = {})
      return nil unless attachment
      return nil unless attachment.content_type&.start_with?('video/', 'audio/')

      # Generate a unique media_id if not provided
      media_id = options[:media_id] || generate_media_id(attachment)

      # Extract metadata
      metadata = extract_metadata(attachment)

      # Create MediaObject
      media_object = MediaObject.create!(
        media_id: media_id,
        context: options[:context] || attachment.context,
        user: options[:user] || attachment.user,
        title: options[:title] || attachment.display_name,
        media_type: determine_media_type(attachment.content_type),
        duration: metadata[:duration],
        attachment_id: attachment.id,
        data: {
          original_filename: attachment.filename,
          content_type: attachment.content_type,
          size: attachment.size,
          width: metadata[:width],
          height: metadata[:height],
          bitrate: metadata[:bitrate],
          created_with: 'custom_media_service'
        }
      )

      # Link attachment to media object (replace "maybe" placeholder)
      attachment.update!(media_entry_id: media_id)

      media_object
    end

    private

    def media_download_url(attachment)
      begin
        Rails.application.routes.url_helpers.file_download_url(
          attachment,
          download: '1',
          inline: '1'
        )
      rescue ArgumentError => e
        # Handle missing host error when running outside web context
        if e.message.include?("Missing host")
          "/files/#{attachment.id}/download?download=1&inline=1"
        else
          raise e
        end
      end
    end

    def generate_media_id(attachment)
      # Generate a unique media ID based on attachment
      "custom_#{attachment.id}_#{SecureRandom.hex(8)}"
    end

    def determine_media_type(content_type)
      case content_type
      when /^video\//
        'video'
      when /^audio\//
        'audio'
      else
        'unknown'
      end
    end

    def ffmpeg_available?
      @ffmpeg_available ||= system('which ffmpeg > /dev/null 2>&1')
    end

    def extract_ffmpeg_metadata(attachment)
      return {} unless attachment.full_filename && File.exist?(attachment.full_filename)

      metadata = {}
      
      begin
        # Use ffprobe to extract metadata
        cmd = [
          'ffprobe',
          '-v', 'quiet',
          '-print_format', 'json',
          '-show_format',
          '-show_streams',
          attachment.full_filename
        ]

        result = `#{cmd.join(' ')} 2>/dev/null`
        data = JSON.parse(result) if result.present?

        if data && data['format']
          metadata[:duration] = data['format']['duration']&.to_f&.round(2)
          metadata[:bitrate] = data['format']['bit_rate']&.to_i
        end

        if data && data['streams']
          video_stream = data['streams'].find { |s| s['codec_type'] == 'video' }
          if video_stream
            metadata[:width] = video_stream['width']&.to_i
            metadata[:height] = video_stream['height']&.to_i
          end
        end
      rescue => e
        Rails.logger.warn "Failed to extract metadata for #{attachment.filename}: #{e.message}"
      end

      metadata
    end

    def extract_width(attachment)
      # Try to extract from ffmpeg metadata
      extract_ffmpeg_metadata(attachment)[:width]
    end

    def extract_height(attachment)
      # Try to extract from ffmpeg metadata
      extract_ffmpeg_metadata(attachment)[:height]
    end

    def extract_duration(attachment)
      # Try to extract from ffmpeg metadata
      extract_ffmpeg_metadata(attachment)[:duration]
    end

    def extract_bitrate(attachment)
      # Try to extract from ffmpeg metadata
      extract_ffmpeg_metadata(attachment)[:bitrate]
    end
  end
end
