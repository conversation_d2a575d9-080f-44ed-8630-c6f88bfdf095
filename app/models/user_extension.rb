# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

# Extension for User model to add system_user method
# This is required for assignment auditing
module UserExtension
  # Returns a system user for auditing purposes
  # This is used when we can't determine the actual user who made a change
  def self.system_user
    @system_user ||= User.find_or_create_by(name: 'System', workflow_state: 'creation_pending') do |u|
      u.workflow_state = 'registered'
      u.sortable_name = 'System'
      u.short_name = 'System'
      u.email = '<EMAIL>'
    end
  end
end

# Add the extension to the User class
User.extend(UserExtension)
