# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

module Auditors::ActiveRecord
  class AssignmentRecord < ActiveRecord::Base
    include Auditors::ActiveRecord::Attributes
    include CanvasPartman::Concerns::Partitioned
    
    self.table_name = "auditor_assignment_records"
    self.partitioning_strategy = :by_date
    self.partitioning_interval = :months
    self.partitioning_field = "created_at"

    belongs_to :assignment, class_name: "::Assignment", inverse_of: :auditor_assignment_records
    belongs_to :user, class_name: "::User", inverse_of: :auditor_assignment_records
    belongs_to :course, class_name: "::Course", inverse_of: :auditor_assignment_records
    belongs_to :account, class_name: "::Account", inverse_of: :auditor_assignment_records

    before_create :generate_uuid
    
    class << self
      include Auditors::ActiveRecord::Model

      def ar_attributes_from_event_stream(record)
        # Start with only the attributes we know we need
        attrs_hash = record.attributes.except("id")
        attrs_hash["uuid"] = record.id
        attrs_hash["assignment_id"] = Shard.relative_id_for(record.assignment_id, Shard.current, Shard.current)
        attrs_hash["user_id"] = Shard.relative_id_for(record.user_id, Shard.current, Shard.current)
        attrs_hash["course_id"] = Shard.relative_id_for(record.course_id, Shard.current, Shard.current)
        attrs_hash["account_id"] = Shard.relative_id_for(record.account_id, Shard.current, Shard.current)
        attrs_hash["created_at"] = record.created_at
        attrs_hash["event_type"] = record.event_type        
        attrs_hash
      end
      
      # Critical method to handle connection invalidation during partition creation
      # This ensures a fresh database connection before trying to create a record
      def create_from_event_stream!(record)
        attrs = ar_attributes_from_event_stream(record)
        
        # Reconnect to ensure a valid connection - this is needed because
        # partition creation can invalidate the existing connection
        self.connection.reconnect!
        
        # Now we can safely create the record with the fresh connection
        create!(attrs)
      end
    end
    
    def generate_uuid
      self.uuid ||= CanvasSlug.generate_securish_uuid
    end
  end
end
