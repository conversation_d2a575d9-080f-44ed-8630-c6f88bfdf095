# frozen_string_literal: true
#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#
class Auditors::Assignment
  class Record < Auditors::Record
    attributes :assignment_id,
              :user_id,
              :course_id,
              :account_id,

    def self.generate(assignment, user, course, event_type)
      raise ArgumentError, "assignment is required" unless assignment

      course_id = course&.id
      user_id = user&.id

      # Generate a UUID for the record
      uuid = SecureRandom.uuid

      # Create a new ActiveRecord directly

      record = new(
        "course" => course,
        "user" => user,
        "event_type" => event_type,
        "created_at" => Time.zone.now,
        "assignment_id" => assignment&.id,
        "user_id" => user_id,
        "course_id" => course_id,
        "account_id" => course&.account_id
      )
      
      Rails.logger.info "[AUDITOR_LOG] Generated record with uuid=#{uuid}, assignment_id=#{record.assignment_id}, user_id=#{record.user_id}"
      
      record
    end

    def initialize(*args)
      super

      @assignment = nil
      @user = nil
      @course = nil
      @account = nil
      @request_id = nil
      @event_type = nil
      @created_at = nil

      if attributes["assignment"]
        @assignment = attributes.delete("assignment")
      end

      if attributes["user"]
        @user = attributes.delete("user")
      end

      if attributes["course"]
        @course = attributes.delete("course")
      end

      if attributes["account"]
        @account = attributes.delete("account")
      end

      if attributes["request_id"]
        @request_id = attributes.delete("request_id")
      end

      if attributes["event_type"]
        @event_type = attributes.delete("event_type")
      end

      if attributes["created_at"]
        @created_at = attributes.delete("created_at")
      end        
    end

    def user
      @user ||= User.find(user_id) if user_id
    end

    def user=(user)
      @user = user

      attributes["user_id"] = Shard.global_id_for(@user)
    end

    def course
      @course ||= Course.find(course_id)
    end

    def course=(course)
      @course = course

      attributes["course_id"] = Shard.global_id_for(@course)
      attributes["account_id"] = Shard.global_id_for(@course.account_id)
    end

    def account
      @account ||= Account.find(account_id)
    end

    def account=(account)
      @account = account

      attributes["account_id"] = Shard.global_id_for(@account)
    end

    def assignment
      @assignment ||= Assignment.find(assignment_id)
    end

    def assignment=(assignment)
      @assignment = assignment

      attributes["assignment_id"] = Shard.global_id_for(@assignment)
    end

    def request_id
      @request_id
    end

    def request_id=(request_id)
      @request_id = request_id

      attributes["request_id"] = Shard.global_id_for(@request_id)
    end

    def created_at
      @created_at
    end

    def event_type
      @event_type
    end
  end

  Stream = Auditors.stream do
    assignment_ar_type = Auditors::ActiveRecord::AssignmentRecord
    record_type assignment_ar_type
    
    add_index :assignment do
      ar_scope_proc ->(assignment) { assignment_ar_type.where(assignment_id: assignment.id) }
    end

    add_index :course do
      ar_scope_proc ->(course) { assignment_ar_type.where(course_id: course.id) }
    end

    add_index :user do
      ar_scope_proc ->(user) { assignment_ar_type.where(user_id: user.id) }
    end

    add_index :account do
      ar_scope_proc ->(account) { assignment_ar_type.where(account_id: account.id) }
    end   
  end

  def self.record_due_date_updated(assignment, user, course)
    return unless assignment

    record(assignment, user, course, "due_date_updated")
  end

  def self.record_email_sent(assignment, user, course, action = 'email_sent')
    return unless assignment

    record(assignment, user, course, action)
  end

  def self.record(assignment, user, course, action)
    return unless assignment

    event_record = nil
    assignment.shard.activate do
      event_record = Auditors::Assignment::Record.generate(assignment, user, course, action)
      Auditors::Assignment::Stream.insert(event_record)
      
      # Use create_from_event_stream! which handles connection reconnection for us
      # rather than going through Stream.insert which can have connection issues
      # Auditors::ActiveRecord::AssignmentRecord.create_from_event_stream!(event_record)
      
      Rails.logger.info("Successfully created audit record for assignment due date change")
    end
    event_record
  end

  def self.apply_common_filters(query, options)
    # Apply event type filter if specified
    if options[:event_type].present?
      event_type_filter = options[:event_type].to_s.downcase
      event_type_filter = '' if event_type_filter == 'all'
      query = query.where(event_type: event_type_filter) if event_type_filter.present?
    end

    # Apply assignment_id filter
    if options[:assignment_id].present?
      query = query.where(assignment_id: options[:assignment_id])
    end

    # Apply user_id filter
    if options[:user_id].present?
      query = query.where(user_id: options[:user_id])
    end

    # Apply date range filters if either is present
    if options[:start_time].present? || options[:end_time].present?
      begin
        start_time = Time.zone.parse(options[:start_time]) if options[:start_time].present?
        end_time = Time.zone.parse(options[:end_time]) if options[:end_time].present?
        
        if start_time && end_time
          # Both start and end time provided - get records between the two dates
          query = query.where(created_at: start_time..end_time)
        elsif start_time
          # Only start time provided - get records on or after start time
          query = query.where('created_at >= ?', start_time)
        elsif end_time
          # Only end time provided - get records on or before end time
          query = query.where('created_at <= ?', end_time)
        end
      rescue ArgumentError => e
        Rails.logger.warn("[Auditors::Assignment.apply_common_filters] Invalid time format. start_time: #{options[:start_time]}, end_time: #{options[:end_time]}. Error: #{e.message}")
      end
    end

    query
  end

  # This method has been consolidated into apply_common_filters

  def self.for_course(course, options = {})
    return [] unless course

    events = []
    course.shard.activate do
      base_query = Auditors::ActiveRecord::AssignmentRecord.where(course_id: course.id)
      events.concat(apply_common_filters(base_query, options).to_a)
      
      # Sort all collected events by created_at descending
      events = events.uniq(&:id).sort_by(&:created_at).reverse
    end
    events
  end

  def self.for_assignment(assignment, options = {})
    return [] unless assignment

    events = []
    assignment.shard.activate do
      base_query = Auditors::ActiveRecord::AssignmentRecord.where(assignment_id: assignment.id)
      events.concat(apply_common_filters(base_query, options).to_a)
      
      # Sort all collected events by created_at descending
      # Using uniq by id before sort_by to avoid issues if records from different tables somehow had same created_at and content otherwise
      events = events.uniq(&:id).sort_by(&:created_at).reverse
    end
    events
  end  

  def self.for_user(user, options = {})
    return [] unless user

    events = []
    user.shard.activate do
      base_query = Auditors::ActiveRecord::AssignmentRecord.where(user_id: user.id)
      events.concat(apply_common_filters(base_query, options).to_a)
      
      # Sort all collected events by created_at descending
      # Using uniq by id before sort_by to avoid issues if records from different tables somehow had same created_at and content otherwise
      events = events.uniq(&:id).sort_by(&:created_at).reverse
    end   
    events
  end

  def self.for_all(options = {})
    return [] unless Account.site_admin

    events = []
    Account.site_admin.shard.activate do
      base_query = Auditors::ActiveRecord::AssignmentRecord
      events.concat(apply_common_filters(base_query, options).to_a)
      
      # Sort all collected events by created_at descending
      # Using uniq by id before sort_by to avoid issues if records from different tables somehow had same created_at and content otherwise
      events = events.uniq(&:id).sort_by(&:created_at).reverse
    end   
    events
  end
end
