class MonthlyReport < ApplicationRecord
  belongs_to :account
  belongs_to :user
  
  validates :month, presence: true, inclusion: { in: 1..12 }
  validates :year, presence: true, numericality: { greater_than: 2000 }
  validates :report_format, presence: true, inclusion: { in: %w[pdf csv xlsx summary] }
  validates :status, presence: true, inclusion: { in: %w[generating completed failed] }
  
  serialize :filters, JSON
  
  scope :completed, -> { where(status: 'completed') }
  scope :for_period, ->(month, year) { where(month: month, year: year) }
  scope :recent, -> { order(created_at: :desc) }
  
  def filename
    account_name = account.name.parameterize
    "#{account_name}_monthly_report_#{month_name}_#{year}.#{file_extension}"
  end
  
  def month_name
    Date::MONTHNAMES[month]
  end
  
  def file_extension
    case report_format
    when 'pdf' then 'pdf'
    when 'csv' then 'csv'
    when 'xlsx' then 'xlsx'
    when 'summary' then 'pdf'
    end
  end
  
  def content_type
    case report_format
    when 'pdf', 'summary' then 'application/pdf'
    when 'csv' then 'text/csv'
    when 'xlsx' then 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    end
  end
  
  def file_path
    Rails.root.join('tmp', 'reports', account.id.to_s).tap do |dir|
      FileUtils.mkdir_p(dir) unless Dir.exist?(dir)
    end.join("#{id}_#{filename}")
  end
  
  def human_file_size
    return 'N/A' unless file_size_bytes
    
    if file_size_bytes < 1.kilobyte
      "#{file_size_bytes} B"
    elsif file_size_bytes < 1.megabyte
      "#{(file_size_bytes / 1.kilobyte.to_f).round(1)} KB"
    else
      "#{(file_size_bytes / 1.megabyte.to_f).round(1)} MB"
    end
  end
  
  # Background job method that integrates with your existing data processing
  def generate_report_file
    begin
      update!(status: 'generating')
      
      # Use the same data processing logic as your existing statistics method
      report_data = fetch_monthly_data_for_report
      
      # Generate the file based on format
      case report_format
      when 'pdf', 'summary'
        generate_pdf_report(report_data)
      when 'csv'
        generate_csv_report(report_data)
      when 'xlsx'
        generate_xlsx_report(report_data)
      end
      
      # Update file size and status
      if File.exist?(file_path)
        update!(
          status: 'completed',
          file_size_bytes: File.size(file_path),
          completed_at: Time.current
        )
        
        # Optionally send notification
        send_completion_notification if should_notify?
      else
        update!(status: 'failed')
      end
      
    rescue StandardError => e
      Rails.logger.error "Failed to generate monthly report #{id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      update!(status: 'failed')
      raise e
    end
  end
  
  private
  
  def fetch_monthly_data_for_report
    # Simulate the same data processing as your existing statistics method
    # but for the specific month/year of this report
    
    # Create a temporary controller context to reuse existing logic
    temp_params = ActionController::Parameters.new({
      month: month,
      year: year,
      filter_role: filters['filter_role'],
      filter_department: filters['filter_department'],
      filter_activity: filters['filter_activity']
    })
    
    # Use the same logic as your existing methods
    start_date = Date.new(year, month, 1).beginning_of_day
    end_date = (Date.new(year, month, 1) + 1.month - 1.day).end_of_day
    
    # Get user IDs using the same logic
    all_user_ids = get_report_user_ids
    
    # Process users using same logic as your existing method
    monthly_users = User.where(id: all_user_ids)
                       .preload(:pseudonyms, :enrollments, :submissions, :account_users)
                       .map { |user| process_report_user_data(user, start_date, end_date) }
    
    # Apply filters
    monthly_users = apply_report_filters(monthly_users, temp_params)
    
    # Calculate statistics
    stats = calculate_report_statistics(monthly_users)
    
    {
      monthly_users: monthly_users,
      statistics: stats,
      start_date: start_date,
      end_date: end_date,
      filters_applied: filters
    }
  end
  
  def get_report_user_ids
    # Same logic as your existing get_monthly_user_ids method
    begin
      enrollment_user_ids = []
      if Course.where(account_id: account.id).exists?
        enrollment_user_ids = User.joins(enrollments: :course)
                                .where(courses: { account_id: account.id })
                                .distinct
                                .pluck(:id)
      end
      
      account_user_ids = account.users.pluck(:id)
      
      if enrollment_user_ids.empty?
        all_system_user_ids = User.all.limit(1000).pluck(:id)
      else
        all_system_user_ids = []
      end
      
      (enrollment_user_ids + account_user_ids + all_system_user_ids).uniq
      
    rescue => e
      Rails.logger.error "Error getting users for report: #{e.message}"
      User.all.limit(1000).pluck(:id)
    end
  end
  
  def process_report_user_data(user, start_date, end_date)
    # Reuse the same logic as your existing process_monthly_user_data method
    roles = get_report_user_roles(user)
    primary_role = determine_report_primary_role(roles)
    department = get_report_user_department(user)
    
    # Get authentication events using the same logic
    auth_data = get_report_authentication_events(user, start_date, end_date)
    
    # Extract authentication data
    all_events = auth_data[:events]
    login_events = all_events.select { |e| e[:event_type] == 'login' }
    logout_events = all_events.select { |e| e[:event_type] == 'logout' }
    login_dates = login_events.map { |event| event[:date] }.uniq.sort
    
    # Get submission data
    submission_count = user.submissions.where(submitted_at: start_date..end_date).count
    submission_details = user.submissions.where(submitted_at: start_date..end_date)
                            .limit(5).pluck(:id, :submitted_at, :submission_type)
    
    # Get additional user info
    sis_id = user.pseudonyms.first&.sis_user_id
    standing_year = user.respond_to?(:student) && user.student&.standing_year
    
    # Check if user has activity this month
    has_activity = all_events.any? || submission_count > 0
    
    {
      user: user,
      role: primary_role,
      all_roles: roles,
      department: department,
      sis_id: sis_id,
      standing_year: standing_year,
      all_events: all_events,
      login_events: login_events,
      logout_events: logout_events,
      login_dates: login_dates,
      days_logged_in: login_dates.size,
      submission_count: submission_count,
      submission_details: submission_details,
      has_activity: has_activity,
      auth_data: auth_data
    }
  end
  
  def get_report_user_roles(user)
    # Same logic as your existing get_monthly_user_roles
    active_enrollments = user.enrollments.active.to_a
    roles = active_enrollments.map do |enrollment|
      case enrollment.type
      when "StudentEnrollment" then "Student"
      when "TeacherEnrollment" then "Teacher"
      when "TaEnrollment" then "TA"
      when "DesignerEnrollment" then "Designer"
      when "ObserverEnrollment" then "Observer"
      else enrollment.type.to_s.demodulize.sub(/Enrollment$/, '')
      end
    end.uniq

    # Add admin role if applicable
    if user.account_users.where(account: account).exists? || user.account_users.exists?
      roles << "Admin"
    end

    # Default role if no other roles found
    roles << "System User" if roles.empty?
    roles
  end
  
  def determine_report_primary_role(roles)
    # Same logic as your existing determine_monthly_primary_role
    return "Admin" if roles.include?("Admin")
    return "Teacher" if roles.include?("Teacher")
    return "TA" if roles.include?("TA")
    return "Student" if roles.include?("Student")
    return "Observer" if roles.include?("Observer")
    return "Designer" if roles.include?("Designer")
    return "System User" if roles.include?("System User")
    "Other"
  end
  
  def get_report_user_department(user)
    # Same logic as your existing get_monthly_user_department
    if user.department.present?
      Department.find_by(id: user.department)&.name || 'Unknown Department'
    else
      'No Department'
    end
  end
  
  def get_report_authentication_events(user, start_date, end_date)
    # Simplified version of your existing authentication events logic
    auth_events = []
    error_messages = []
    
    begin
      user.pseudonyms.each do |pseudonym|
        begin
          # Try Canvas Auditors API first
          auditor_records = Auditors::Authentication.for_pseudonym(pseudonym)
          
          if auditor_records.respond_to?(:paginate)
            page = auditor_records.paginate(per_page: 100)
            page.each do |record|
              if record.created_at >= start_date && record.created_at <= end_date
                auth_events << {
                  date: record.created_at.to_date,
                  time: record.created_at,
                  event_type: record.event_type,
                  pseudonym_id: pseudonym.id,
                  source: 'auditors'
                }
              end
            end
          elsif auditor_records.respond_to?(:each)
            auditor_records.each do |record|
              if record.created_at >= start_date && record.created_at <= end_date
                auth_events << {
                  date: record.created_at.to_date,
                  time: record.created_at,
                  event_type: record.event_type,
                  pseudonym_id: pseudonym.id,
                  source: 'auditors'
                }
              end
            end
          end
        rescue => e
          Rails.logger.warn "Auditors API failed for report generation: #{e.message}"
        end
      end
    rescue => e
      Rails.logger.error "Error getting authentication events for report: #{e.message}"
    end
    
    # Sort events by time
    auth_events.sort_by! { |event| event[:time] }
    
    {
      events: auth_events,
      errors: error_messages,
      total_logins: auth_events.count { |e| e[:event_type] == 'login' },
      total_logouts: auth_events.count { |e| e[:event_type] == 'logout' },
      source_used: auth_events.empty? ? 'none' : auth_events.first[:source]
    }
  end
  
  def apply_report_filters(monthly_users, temp_params)
    # Same logic as your existing apply_monthly_filters
    return monthly_users unless monthly_users
    
    # Filter by role
    if temp_params[:filter_role].present?
      if temp_params[:filter_role] == 'Faculty'
        monthly_users = monthly_users.select { |u| (u[:all_roles] & ['Admin', 'Teacher', 'TA']).any? }
      else
        monthly_users = monthly_users.select { |u| u[:all_roles].include?(temp_params[:filter_role]) || u[:role] == temp_params[:filter_role] }
      end
    end

    # Filter by activity
    if temp_params[:filter_activity] == 'active'
      monthly_users = monthly_users.select { |u| u[:has_activity] }
    elsif temp_params[:filter_activity] == 'inactive'
      monthly_users = monthly_users.reject { |u| u[:has_activity] }
    end

    # Filter by department
    if temp_params[:filter_department].present?
      monthly_users = monthly_users.select { |u| u[:department] == temp_params[:filter_department] }
    end
    
    monthly_users
  end
  
  def calculate_report_statistics(monthly_users)
    # Same logic as your existing calculate_monthly_summary_stats
    return {} unless monthly_users
    
    active_students = monthly_users.count { |u| u[:role] == 'Student' && u[:has_activity] }
    active_faculty = monthly_users.count { |u| ['Teacher', 'TA', 'Admin'].include?(u[:role]) && u[:has_activity] }
    total_active = monthly_users.count { |u| u[:has_activity] }
    total_auth_events = monthly_users.sum { |u| u[:all_events].count }
    
    # Count total login days across all users
    total_login_days = monthly_users.sum { |u| u[:days_logged_in] }
    
    # Keep the raw login events count for reference
    total_logins = monthly_users.sum { |u| u[:login_events].count }
    total_logouts = monthly_users.sum { |u| u[:logout_events].count }
    total_submissions = monthly_users.sum { |u| u[:submission_count] }

    # Calculate unique login days across all users
    all_login_dates = Set.new
    monthly_users.each do |user_data|
      user_data[:login_dates].each { |date| all_login_dates.add(date) }
    end
    unique_login_days = all_login_dates.size
    
    {
      active_students: active_students,
      active_faculty: active_faculty,
      total_active: total_active,
      total_auth_events: total_auth_events,
      total_login_days: total_login_days,
      total_logins: total_logins,
      total_logouts: total_logouts,
      total_submissions: total_submissions,
      unique_login_days: unique_login_days,
      total_users: monthly_users.count
    }
  end
  
  def generate_pdf_report(report_data)
    require 'prawn'
    
    Prawn::Document.generate(file_path) do |pdf|
      # Title
      pdf.text "Monthly Activity Report", size: 20, style: :bold, align: :center
      pdf.text "#{month_name} #{year}", size: 16, align: :center
      pdf.text "Account: #{account.name}", size: 14, align: :center
      pdf.move_down 20
      
      # Summary Statistics
      pdf.text "Summary Statistics", size: 16, style: :bold
      pdf.move_down 10
      
      stats = report_data[:statistics]
      summary_data = [
        ["Total Users Analyzed", stats[:total_users]],
        ["Active Students", stats[:active_students]],
        ["Active Faculty (Teachers/TAs/Admins)", stats[:active_faculty]],
        ["Total Active Users", stats[:total_active]],
        ["Total Login Days", stats[:total_login_days]],
        ["Unique Calendar Days with Logins", stats[:unique_login_days]],
        ["Total Login Events", stats[:total_logins]],
        ["Total Submissions", stats[:total_submissions]]
      ]
      
      pdf.table(summary_data, width: 400) do
        cells.borders = [:bottom]
        cells.border_width = 0.5
        cells.padding = [5, 10]
        column(0).font_style = :bold
        column(0).width = 250
        column(1).width = 150
      end
      
      pdf.move_down 20
      
      # Applied Filters
      if report_data[:filters_applied].any?
        pdf.text "Applied Filters", size: 14, style: :bold
        pdf.move_down 5
        
        filter_text = []
        filter_text << "Role: #{report_data[:filters_applied]['filter_role']}" if report_data[:filters_applied]['filter_role'].present?
        filter_text << "Department: #{report_data[:filters_applied]['filter_department']}" if report_data[:filters_applied]['filter_department'].present?
        filter_text << "Activity: #{report_data[:filters_applied]['filter_activity']}" if report_data[:filters_applied]['filter_activity'].present?
        filter_text << "Include Inactive: Yes" if report_data[:filters_applied]['include_inactive']
        filter_text << "Include All Roles: Yes" if report_data[:filters_applied]['include_all_roles']
        filter_text << "Include Detailed Logs: Yes" if report_data[:filters_applied]['include_detailed_logs']
        
        pdf.text filter_text.join(" | "), size: 12
        pdf.move_down 15
      end
      
      # Detailed User Data (if requested)
      if filters['include_detailed_logs'] && report_data[:monthly_users].any?
        pdf.text "Detailed User Activity", size: 16, style: :bold
        pdf.move_down 10
        
        # Create table data
        table_data = [["Name", "Role", "Department", "Login Days", "Submissions", "SIS ID"]]
        
        report_data[:monthly_users].each do |user_data|
          table_data << [
            user_data[:user].name || 'N/A',
            user_data[:role] || 'N/A',
            user_data[:department] || 'N/A',
            user_data[:days_logged_in].to_s,
            user_data[:submission_count].to_s,
            user_data[:sis_id] || 'N/A'
          ]
        end
        
        if table_data.length > 1
          pdf.table(table_data, header: true, width: pdf.bounds.width) do
            row(0).font_style = :bold
            cells.borders = [:bottom]
            cells.border_width = 0.5
            cells.padding = [3, 5]
            cells.size = 9
          end
        end
      end
      
      # Footer
      pdf.move_down 20
      pdf.text "Generated on #{Time.current.strftime('%B %d, %Y at %I:%M %p')}", 
               size: 10, align: :right, style: :italic
      pdf.text "Report ID: #{id}", size: 10, align: :right, style: :italic
    end
  end
  
  def generate_csv_report(report_data)
    require 'csv'
    
    CSV.open(file_path, 'w') do |csv|
      # Header information
      csv << ["Monthly Activity Report - #{month_name} #{year}"]
      csv << ["Account: #{account.name}"]
      csv << ["Generated: #{Time.current.strftime('%B %d, %Y at %I:%M %p')}"]
      csv << ["Report ID: #{id}"]
      csv << []
      
      # Summary statistics
      stats = report_data[:statistics]
      csv << ['Summary Statistics']
      csv << ['Total Users Analyzed', stats[:total_users]]
      csv << ['Active Students', stats[:active_students]]
      csv << ['Active Faculty', stats[:active_faculty]]
      csv << ['Total Active Users', stats[:total_active]]
      csv << ['Total Login Days', stats[:total_login_days]]
      csv << ['Unique Calendar Days with Logins', stats[:unique_login_days]]
      csv << ['Total Login Events', stats[:total_logins]]
      csv << ['Total Submissions', stats[:total_submissions]]
      csv << []
      
      # Applied filters
      if report_data[:filters_applied].any?
        csv << ['Applied Filters']
        csv << ['Role Filter', report_data[:filters_applied]['filter_role'] || 'None']
        csv << ['Department Filter', report_data[:filters_applied]['filter_department'] || 'None']
        csv << ['Activity Filter', report_data[:filters_applied]['filter_activity'] || 'None']
        csv << []
      end
      
      # User details
      csv << ['User Details']
      csv << ['Name', 'Email', 'Role', 'Department', 'SIS ID', 'Login Days', 'Login Dates', 'Submissions', 'Has Activity']
      
      report_data[:monthly_users].each do |user_data|
        login_dates = user_data[:login_dates].map { |d| d.strftime('%m/%d') }.join(', ')
        
        csv << [
          user_data[:user].name || 'N/A',
          user_data[:user].email || 'N/A',
          user_data[:role] || 'N/A',
          user_data[:department] || 'N/A',
          user_data[:sis_id] || 'N/A',
          user_data[:days_logged_in],
          login_dates,
          user_data[:submission_count],
          user_data[:has_activity] ? 'Yes' : 'No'
        ]
      end
    end
  end
  
  def generate_xlsx_report(report_data)
    require 'rubyXL'
    
    workbook = RubyXL::Workbook.new
    worksheet = workbook[0]
    worksheet.sheet_name = "Monthly Report #{month_name} #{year}"
    
    row = 0
    
    # Title and header info
    worksheet.add_cell(row, 0, "Monthly Activity Report - #{month_name} #{year}")
    worksheet.sheet_data[row][0].change_font_bold(true)
    worksheet.sheet_data[row][0].change_font_size(16)
    row += 1
    
    worksheet.add_cell(row, 0, "Account: #{account.name}")
    row += 1
    
    worksheet.add_cell(row, 0, "Generated: #{Time.current.strftime('%B %d, %Y at %I:%M %p')}")
    row += 2
    
    # Summary Statistics
    worksheet.add_cell(row, 0, 'Summary Statistics')
    worksheet.sheet_data[row][0].change_font_bold(true)
    row += 1
    
    stats = report_data[:statistics]
    summary_items = [
      ['Total Users Analyzed', stats[:total_users]],
      ['Active Students', stats[:active_students]],
      ['Active Faculty', stats[:active_faculty]],
      ['Total Active Users', stats[:total_active]],
      ['Total Login Days', stats[:total_login_days]],
      ['Unique Calendar Days with Logins', stats[:unique_login_days]],
      ['Total Login Events', stats[:total_logins]],
      ['Total Submissions', stats[:total_submissions]]
    ]
    
    summary_items.each do |label, value|
      worksheet.add_cell(row, 0, label)
      worksheet.add_cell(row, 1, value)
      worksheet.sheet_data[row][0].change_font_bold(true)
      row += 1
    end
    
    row += 1
    
    # User details headers
    headers = ['Name', 'Email', 'Role', 'Department', 'SIS ID', 'Login Days', 'Submissions', 'Has Activity']
    headers.each_with_index do |header, col|
      worksheet.add_cell(row, col, header)
      worksheet.sheet_data[row][col].change_font_bold(true)
    end
    row += 1
    
    # User data
    report_data[:monthly_users].each do |user_data|
      worksheet.add_cell(row, 0, user_data[:user].name || 'N/A')
      worksheet.add_cell(row, 1, user_data[:user].email || 'N/A')
      worksheet.add_cell(row, 2, user_data[:role] || 'N/A')
      worksheet.add_cell(row, 3, user_data[:department] || 'N/A')
      worksheet.add_cell(row, 4, user_data[:sis_id] || 'N/A')
      worksheet.add_cell(row, 5, user_data[:days_logged_in])
      worksheet.add_cell(row, 6, user_data[:submission_count])
      worksheet.add_cell(row, 7, user_data[:has_activity] ? 'Yes' : 'No')
      row += 1
    end
    
    workbook.write(file_path)
  end
  
  def should_notify?
    # Check if notifications are enabled for this account/user
    user.preferences&.dig('monthly_report_notifications') || false
  end
  
  def send_completion_notification
    # Send email notification that report is ready
    begin
      # You can implement your notification system here
      Rails.logger.info "Report #{id} completed - notification would be sent to #{user.email}"
    rescue => e
      Rails.logger.error "Failed to send notification for report #{id}: #{e.message}"
    end
  end
end

# Migration file
# Create this file: db/migrate/xxx_create_monthly_reports.rb

class CreateMonthlyReports < ActiveRecord::Migration[6.1]
  def change
    create_table :monthly_reports do |t|
      t.references :account, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.integer :month, null: false
      t.integer :year, null: false
      t.string :report_format, null: false
      t.text :filters
      t.string :status, default: 'generating'
      t.integer :file_size_bytes
      t.boolean :auto_generated, default: false
      t.datetime :completed_at
      t.timestamps
    end
    
    add_index :monthly_reports, [:account_id, :month, :year]
    add_index :monthly_reports, [:account_id, :status]
    add_index :monthly_reports, :created_at
  end
end