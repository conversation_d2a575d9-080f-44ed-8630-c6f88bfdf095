# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.

class LateAssignmentNotification < ActiveRecord::Base
  belongs_to :assignment
  belongs_to :user
  
  # Notification types
  LATE = 'late'.freeze
  UPCOMING = 'upcoming'.freeze

  # Find if a notification has already been sent to a user for an assignment
  # If the assignment's due date has changed since the last notification, we consider it not sent
  def self.already_sent?(assignment_id, user_id, notification_type = LATE)
    assignment = Assignment.find_by(id: assignment_id)
    return false unless assignment&.due_at
    
    # Convert due_at to date for comparison (ignoring time component)
    current_due_date = assignment.due_at.to_date
    
    # Check if we have a notification with matching assignment, user, type AND due_date
    exists?(
      assignment_id: assignment_id,
      user_id: user_id,
      notification_type: notification_type,
      due_date: current_due_date
    )
  end
  
  # Mark that a notification has been sent to a user for an assignment
  def self.mark_as_sent(assignment_id, user_id, notification_type = LATE)
    assignment = Assignment.find_by(id: assignment_id)
    return false unless assignment&.due_at
    
    # Store the current due date (date only, not time) when marking as sent
    current_due_date = assignment.due_at.to_date
    
    create(
      assignment_id: assignment_id,
      user_id: user_id,
      notification_type: notification_type,
      due_date: current_due_date,
      sent_at: Time.now.utc
    )
  end

  def self.delete_sent_notifications(assignment_id, user_id, notification_types = nil)
    query = where(assignment_id: assignment_id, user_id: user_id)
    query = query.where(notification_type: Array(notification_types)) if notification_types
    query.delete_all
  end
end
