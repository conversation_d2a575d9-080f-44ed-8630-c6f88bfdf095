/* Syllabus Reading Requirement Styles */

/* Locked tab styling */
.section-locked, li.locked {
  opacity: 0.6;

  a {
    color: #999 !important;
    cursor: not-allowed !important;
    text-decoration: none !important;

    &:hover {
      color: #999 !important;
      text-decoration: none !important;
    }

    &.locked {
      position: relative;

      .nav-icon.icon-lock {
        color: #d9534f;
        margin-left: 5px;
        font-size: 0.9em;
      }
    }
  }
}

/* Additional styling for section tabs */
#section-tabs li.locked {
  opacity: 0.5;

  a {
    color: #888 !important;
    cursor: not-allowed !important;
    pointer-events: none;

    &:hover, &:focus {
      color: #888 !important;
      text-decoration: none !important;
    }
  }
}

/* Tooltip styling for locked tabs */
.section-locked a[data-tooltip] {
  position: relative;
  
  &:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    white-space: nowrap;
    z-index: 1000;
    font-size: 12px;
    margin-bottom: 5px;
  }
  
  &:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
    z-index: 1000;
  }
}

/* Flash message styling for syllabus requirement */
.flash-message.syllabus-requirement {
  background-color: #d9edf7;
  border-color: #bce8f1;
  color: #31708f;
  
  .icon-info {
    color: #31708f;
  }
}

/* Syllabus completion indicator */
.syllabus-completion-message {
  background: #dff0d8;
  border: 1px solid #d6e9c6;
  color: #3c763d;
  padding: 10px 15px;
  border-radius: 4px;
  margin: 10px 0;
  
  .icon-check {
    color: #5cb85c;
    margin-right: 5px;
  }
}

/* Progress indicator for syllabus reading */
.syllabus-progress {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 20px;
  font-size: 12px;
  z-index: 1000;
  transition: opacity 0.3s ease;
  
  &.hidden {
    opacity: 0;
    pointer-events: none;
  }
  
  .progress-bar {
    width: 100px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin: 5px 0;
    overflow: hidden;
    
    .progress-fill {
      height: 100%;
      background: #5cb85c;
      transition: width 0.3s ease;
      border-radius: 2px;
    }
  }
}
