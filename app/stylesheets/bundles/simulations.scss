.simulations-container {
  padding: 16px 24px;
  max-width: 1400px;
  width: 100%;
}

.simulations-list {
  width: 100%;
}

/* Search and Add Section */
.search-add-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
  justify-content: flex-start;
  width: 100%;
}

.search-bar-row {
  display: flex;
  width: 100%;
  gap: 32px;
  align-items: center;
}

.search-container {
  position: relative;
  flex: 1 1 0;
  min-width: 0;
  width: auto;
}

.search-input {
  width: 100%;
  text-align: center;
  padding-left: 48px;
  margin-bottom: 0 !important;
}

.search-input:focus {
  outline: none;
  border-color: #14532d;
  background-color: white;
  box-shadow: 0 0 0 3px rgba(20, 83, 45, 0.1);
}

.search-input::placeholder {
  text-align: left;
  color: #9ca3af;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  width: 20px;
  height: 20px;
}

.add-btn {
  flex-shrink: 0;
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  white-space: nowrap;
}

.section-title {
  color: #14532d;
  border-left: 5px solid #14532d;
  padding-left: 12px;
  margin-bottom: 16px;
  font-size: 1.5rem;
  font-weight: 600;
}

.simulation-add-form {
  border-radius: 10px;
  margin-bottom: 20px;
}

.simulation-item {
  background: #e8f5e9;
  border: 2px solid #14532d;
  border-radius: 10px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(20, 83, 45, 0.06);
  padding: 0;
  overflow: visible !important;
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.simulation-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 32px 0 32px;
  gap: 20px;
  position: relative;
}

.simulation-title-link {
  text-decoration: underline;
  color: #14532d;
  font-weight: 700;
  font-size: 1.15rem;
}

.simulation-title-link:hover {
  color: #256d3a;
}

.simulation-actions {
  display: flex;
  gap: 10px;
  position: relative;
  overflow: visible !important;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.5em;
  padding: 4px 8px;
  color: #14532d;
}

.dropdown-menu {
  display: none;
  position: absolute;
  left: -80px;
  top: 32px;
  background: #fff;
  border: 1px solid #b2dfdb;
  border-radius: 8px;
  min-width: 160px;
  box-shadow: 0 4px 16px rgba(20, 83, 45, 0.10);
  z-index: 10;
  padding: 8px 0;
  overflow: visible;
}

.dropdown.open .dropdown-menu {
  display: block;
}

.dropdown-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  padding: 10px 20px;
  color: #14532d;
  font-size: 1em;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.15s, color 0.15s;
  border-radius: 0;
  margin: 0;
}

.dropdown-item .icon-edit,
.dropdown-item .icon-delete {
  font-size: 1.2em;
  width: 20px;
  height: 20px;
}

/* Edit button styles */
.dropdown-item.edit-simulation-btn {
  color: #14532d;
}

.dropdown-item.edit-simulation-btn .icon-edit {
  color: #14532d;
}

.dropdown-item.edit-simulation-btn:hover,
.dropdown-item.edit-simulation-btn:focus {
  background: #14532d;
  color: #fff;
}

.dropdown-item.edit-simulation-btn:hover .icon-edit,
.dropdown-item.edit-simulation-btn:focus .icon-edit {
  color: #fff;
}

/* Delete button styles - cleaned up and consolidated */
.dropdown-item.btn-danger {
  color: #14532d !important;
  background: transparent !important;
}

.dropdown-item.btn-danger .icon-delete {
  color: #14532d !important;
}

.dropdown-item.btn-danger:hover,
.dropdown-item.btn-danger:focus {
  background: #14532d !important;
  color: #fff !important;
}

.dropdown-item.btn-danger:hover .icon-delete,
.dropdown-item.btn-danger:focus .icon-delete {
  color: #fff !important;
}

.simulation-card-body {
  padding: 12px 32px 24px 32px;
}

.simulation-label {
  font-weight: 600;
  color: #14532d;
  margin-right: 6px;
}

.simulation-item h3 {
  margin-top: 0;
  color: #14532d;
  font-weight: 700;
}

.simulation-item p {
  color: #14532d;
  margin-bottom: 15px;
}

/* Empty State Styles */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 36px 28px;
  background: #f8fffe;
  border: 2px dashed #14532d;
  border-radius: 10px;
  margin: 16px 0;
  width: 100%;
}

.empty-state-content {
  display: flex;
  align-items: center;
  gap: 24px;
  text-align: left;
}

.empty-state-icon {
  flex-shrink: 0;
  opacity: 0.8;
}

.empty-state-text {
  flex: 1;
}

.empty-state-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #14532d;
  margin: 0 0 8px 0;
}

.empty-state-subtitle {
  font-size: 0.95rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* Button Styles */
.simulation-item .btn,
.simulation-add-form .btn-primary,
.btn-danger {
  display: inline-block;
  padding: 8px 16px;
  font-weight: 600;
  border-radius: 6px;
  border: none;
  text-decoration: none;
  cursor: pointer;
  font-size: 0.9em;
  line-height: 1.2;
  box-sizing: border-box;
}

.simulation-item .btn {
  background-color: #14532d;
  color: #fff;
  transition: background 0.2s;
}

.simulation-item .btn:hover {
  background-color: #256d3a;
  text-decoration: none;
}

.simulation-add-form label {
  color: #14532d;
  font-weight: 600;
}

.simulation-add-form input[type="text"],
.simulation-add-form input[type="url"] {
  width: 500px !important;
  max-width: 100%;
}

.simulation-add-form .btn-primary {
  background-color: #14532d;
  color: #fff;
}

.simulation-add-form .btn-primary:hover {
  background-color: #256d3a;
}

.btn-danger {
  border: none;
  border-radius: 6px;
  padding: 10px 24px;
  font-weight: 600;
  font-size: 1em;
  cursor: pointer;
  transition: background 0.2s;
}

.btn-danger:hover {
  background-color: #b91c1c;
}

.btn-primary {
  background-color: #14532d;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 24px;
  font-weight: 600;
  font-size: 1em;
  cursor: pointer;
  transition: background 0.2s;
}

.btn-primary:hover {
  background-color: #256d3a;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(20, 83, 45, 0.15);
  z-index: 1000;
}

.modal {
  position: fixed;
  top: 50%; left: 50%;
  transform: translate(-50%, -50%);
  background: #f4f8f4;
  border: 2px solid #14532d;
  border-radius: 8px;
  box-shadow: 0 4px 24px rgba(20, 83, 45, 0.10);
  z-index: 1001;
  max-width: 600px;
  width: 90%;
}

.modal-content {
  padding: 20px;
  position: relative;
  max-width: 600px; 
}

.auto-expand-textarea {
  resize: none;
  overflow-y: auto;
  min-height: 2.5em;
  max-height: 40vh;
  line-height: 1.4;
  width: 100%;
  box-sizing: border-box;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 18px;
  width: 100%;
}

.modal-close {
  position: absolute;
  top: 12px;      
  right: 20px;    
  font-size: 2em;
  color: #14532d;
  cursor: pointer;
  z-index: 10;
  background: none;
  border: none;
  line-height: 1;
  padding: 0;
}

.modal input[type="text"],
.modal input[type="url"] {
  width: 540px !important;
  max-width: 100%;
}

/* Responsive Design */
@media (max-width: 1400px) {
  .search-container {
    width: 1000px;
  }
}

@media (max-width: 1200px) {
  .search-container {
    width: 800px;
  }
}

@media (max-width: 1024px) {
  .search-container {
    width: 700px;
  }
  
  .simulation-item {
    max-width: 900px;
  }
}

@media (max-width: 768px) {
  .simulations-container {
    padding: 12px 16px;
  }
  
  .search-add-section {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .search-bar-row {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .search-container {
    width: 100%;
  }
  
  .search-input {
    height: 40px;
    padding: 0 14px 0 42px;
    font-size: 0.95rem;
  }
  
  .search-input:focus {
    padding-left: 42px;
  }
  
  .search-icon {
    left: 14px;
    width: 18px;
    height: 18px;
  }
  
  .add-btn {
    width: 100%;
    justify-content: center;
    height: 40px;
  }
  
  .section-title {
    font-size: 1.25rem;
    margin-bottom: 12px;
  }
  
  .simulation-item {
    max-width: 100%;
  }
  
  .simulation-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 18px 8px 18px;
  }
  
  .simulation-actions {
    align-self: stretch;
    justify-content: flex-end;
  }
  
  .simulation-card-body {
    padding: 0 18px 16px 18px;
  }
  
  .simulation-title {
    font-size: 1.1rem;
  }
  
  .empty-state {
    padding: 24px 20px;
    max-width: 100%;
  }
  
  .empty-state-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .empty-state-icon svg {
    width: 60px;
    height: 60px;
  }
  
  .empty-state-title {
    font-size: 1.15rem;
  }
  
  .empty-state-subtitle {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .simulations-container {
    padding: 8px 12px;
  }
  
  .search-input {
    height: 36px;
    padding: 0 12px 0 38px;
    font-size: 0.9rem;
  }
  
  .search-input:focus {
    padding-left: 38px;
  }
  
  .search-icon {
    left: 12px;
    width: 16px;
    height: 16px;
  }
  
  .add-btn {
    height: 36px;
    font-size: 0.9rem;
  }
  
  .simulation-card-header {
    padding: 14px 16px 6px 16px;
  }
  
  .simulation-card-body {
    padding: 0 16px 14px 16px;
  }
  
  .simulation-item .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

.icon-edit::before {
  content: none !important;
}
.dropdown-menu form {
  margin: 0 !important;
}