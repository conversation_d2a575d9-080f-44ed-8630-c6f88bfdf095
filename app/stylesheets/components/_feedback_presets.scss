/*
 * Copyright (C) 2024 - present Instructure, Inc.
 * Feedback Preset Buttons - Stacked layout with emojis below
 */

// Main container for stacked emoji and preset layout
.emoji-attachments-stacked {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin: 0;
  padding: 0;
}

// First row: Feedback presets and attachment buttons
.feedback-preset-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  justify-content: space-between; // Spread content across the row
}

// Second row: Individual emoji picker
.emoji-picker-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}

// Feedback preset container - now in first row
.feedback-preset-container {
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 0;
  padding: 0;
  flex-wrap: nowrap;
  flex: 1; // Take available space

  &.inline-presets {
    margin-left: 0;
    padding-left: 0;
  }
}

.feedback-preset-button {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  padding: 3px 6px;
  border: 1px solid #d1d5da;
  border-radius: 3px;
  background: #f6f8fa;
  color: #24292e;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  white-space: nowrap;
  text-decoration: none;
  line-height: 1.1;
  min-height: 24px;

  &:hover {
    background: #0366d6;
    border-color: #0366d6;
    color: rgb(0, 0, 0);
    transform: translateY(-1px);
    box-shadow: 0 1px 3px rgba(3, 102, 214, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(3, 102, 214, 0.4);
  }

  &:focus {
    outline: none;
    border-color: #0366d6;
    box-shadow: 0 0 0 2px rgba(3, 102, 214, 0.2);
  }

  .preset-emoji {
    font-size: 12px;
    line-height: 1;
  }

  .preset-text {
    font-weight: 500;
  }
}

// Individual emoji picker styling (👍👏😀)
#emoji-quick-picker-container {
  .emoji-quick-picker {
    display: flex;
    align-items: center;
    gap: 6px;

    button {
      line-height: 1;
      transition: all 0.1s ease-in-out;
      font-size: 18px;
      padding: 2px 4px;
      border: none;
      background: transparent;
      cursor: pointer;

      &:focus-within {
        transform: scale(1.2);
      }

      &:hover {
        transform: scale(1.2);
      }
    }

    span {
      width: 20px;
    }
  }
}

// Attachment buttons styling
.attach_things {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0; // Don't shrink

  button {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    padding: 4px 8px;
    border: 1px solid #c7cdd1;
    border-radius: 3px;
    background: #f5f5f5;
    color: #2d3b45;
    font-size: 12px;
    line-height: 1.2;
    cursor: pointer;
    transition: all 0.15s ease;

    &:hover {
      background: #e8e8e8;
      border-color: #a8b0b5;
    }

    &:focus {
      outline: 2px solid #0374b5;
      outline-offset: 2px;
    }

    i {
      font-size: 14px;
    }
  }
}

// Integration with Canvas action header
.ic-Action-header {
  display: flex;
  align-items: flex-start; // Allow for stacked content
  gap: 12px;
  flex-wrap: nowrap;

  .emoji-attachments-stacked {
    flex: 1; // Take available space
    order: 1;
  }

  .ic-Action-header__Secondary {
    order: 2;
    flex-shrink: 0;
    align-self: flex-start; // Align to top of the stacked content
    margin-top: 0; // Reset any existing margin

    #comment_submit_button {
      background: #00843d;
      border-color: #00843d;
      color: white;
      padding: 8px 16px;
      border-radius: 3px;
      font-weight: 500;
      font-size: 14px;

      &:hover {
        background: #00752e;
        border-color: #00752e;
      }
    }
  }
}

// Ensure proper spacing in comment area
#add_a_comment {
  .emoji-attachments-stacked {
    margin-top: 4px;
    margin-bottom: 4px;
  }

  .ic-Action-header {
    margin-top: 8px;
    align-items: flex-start; // Important for proper alignment
  }
}

// Mobile responsive - maintain stacked layout but adjust sizes
@media (max-width: 480px) {
  .emoji-attachments-stacked {
    gap: 4px;
  }

  .feedback-preset-row {
    flex-wrap: wrap;
    gap: 4px;
  }

  .feedback-preset-container {
    width: 100%;
    justify-content: flex-start;

    .feedback-preset-button {
      font-size: 10px;
      padding: 2px 5px;
    }
  }

  .emoji-picker-row {
    gap: 3px;

    #emoji-quick-picker-container .emoji-quick-picker button {
      font-size: 16px;
    }
  }

  .attach_things {
    width: 100%;
    justify-content: flex-start;
    margin-top: 4px;
  }

  .ic-Action-header {
    flex-direction: column;
    align-items: stretch;

    .ic-Action-header__Secondary {
      order: 3;
      margin-top: 8px;
      align-self: stretch;

      #comment_submit_button {
        width: 100%;
      }
    }
  }
}

// Tablet size - keep stacked but smaller
@media (max-width: 768px) and (min-width: 481px) {
  .feedback-preset-button {
    font-size: 10px;
    padding: 2px 5px;
    gap: 2px;

    .preset-emoji {
      font-size: 11px;
    }
  }

  #emoji-quick-picker-container .emoji-quick-picker button {
    font-size: 16px;
  }

  .attach_things button {
    font-size: 11px;
    padding: 3px 6px;
  }
}
