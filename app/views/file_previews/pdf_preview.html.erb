<!DOCTYPE html>
<html>
<head>
  <title><%= @file&.display_name || 'PDF Viewer' %></title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow: hidden;
      font-family: Arial, sans-serif;
    }
    #pdf-viewer {
      width: 100%;
      height: 100vh;
      border: none;
    }
    .debug-info {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0,0,0,0.8);
      color: white;
      padding: 10px;
      font-size: 12px;
      display: none;
    }
    .debug-toggle {
      position: fixed;
      bottom: 10px;
      right: 10px;
      background: #f00;
      color: white;
      border: none;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      cursor: pointer;
      z-index: 1000;
    }
    .pdf-fallback {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      padding: 40px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .btn {
      display: inline-block;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 4px;
      margin: 5px;
      font-weight: bold;
    }
    .btn-primary {
      background: #007bff;
      color: white;
    }
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
  </style>
</head>
<body>
  <%
    pdf_url = @pdf_url.to_s
    unless pdf_url.start_with?('http')
      pdf_url = "#{request.protocol}#{request.host_with_port}#{pdf_url}"
    end    
  %>

  <iframe
    id="pdf-viewer"
    src="<%= pdf_url %>#view=FitH"
    type="application/pdf"
    title="<%= @file&.display_name || 'PDF Viewer' %>"
    allowfullscreen="true">
  
    <div class="pdf-fallback">
      <h3>PDF Preview</h3>
      <p>Your browser doesn't support embedded PDF viewing or the PDF is being downloaded instead.</p>
      <div style="margin-top: 20px;">
        <a href="<%= pdf_url %>" target="_blank" class="btn btn-primary">
          Open in New Tab
        </a>
        <a href="<%= pdf_url %>" download class="btn btn-secondary">
          Download PDF
        </a>
      </div>
      <div style="margin-top: 15px; font-size: 12px; color: #666;">
        <strong>Troubleshooting:</strong><br>
        • Check if your browser is set to download PDFs automatically<br>
        • Try opening the PDF in a new tab using the button above<br>
        • Some browsers may require enabling PDF viewing in settings
      </div>
    </div>
  </iframe>
  
  <button class="debug-toggle" onclick="document.querySelector('.debug-info').style.display = 'block';">i</button>
  <div class="debug-info">
    <strong>Debug Info:</strong><br>
    PDF URL: <%= pdf_url %><br>
    Viewer Type: Native Browser PDF Viewer<br>
    Content Type: <%= @file&.content_type %><br>
    <button onclick="document.querySelector('.debug-info').style.display = 'none';">Hide</button>
  </div>
  
  <script>
    document.getElementById('pdf-viewer').onload = function() {
      setTimeout(function() {
        var fallback = document.querySelector('.pdf-fallback');
        if (fallback) {
          fallback.style.display = 'none';
        }
      }, 1000);
    };

    document.getElementById('pdf-viewer').onerror = function() {
      var fallback = document.querySelector('.pdf-fallback');
      if (fallback) {
        fallback.style.display = 'block';
      }
    };
  </script>
</body>
</html> 