<%
# Copyright (C) 2014 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>

<style>
  .multi-page-viewer {
    height: 100vh;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
  }
  
  .page-controls {
    background-color: #333;
    color: white;
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    flex-shrink: 0;
  }
  
  .page-controls button {
    background-color: #555;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  }
  
  .page-controls button:hover:not(:disabled) {
    background-color: #777;
  }
  
  .page-controls button:disabled {
    background-color: #333;
    color: #666;
    cursor: not-allowed;
  }
  
  .page-info {
    font-size: 14px;
    color: #ccc;
  }
  
  .page-content {
    flex: 1;
    overflow-y: auto;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
  }
  
  .page-image {
    max-width: 100%;
    max-height: none;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border: 1px solid #ddd;
    background-color: white;
  }
  
  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #666;
    font-size: 16px;
  }
</style>

<body class="multi-page-viewer">
  <div class="page-controls">
    <button id="prevBtn" onclick="previousPage()">← Previous</button>
    <span class="page-info">
      Page <span id="currentPage">1</span> of <span id="totalPages"><%= @page_count %></span>
    </span>
    <button id="nextBtn" onclick="nextPage()">Next →</button>
  </div>
  
  <div class="page-content">
    <div id="loadingIndicator" class="loading">Loading page...</div>
    <img id="pageImage" class="page-image" style="display: none;" alt="<%= @file.display_name %>" />
  </div>

  <script>
    let currentPageNum = 1;
    const totalPages = <%= @page_count %>;
    const baseUrl = '<%= @base_image_url %>';
    const pageImage = document.getElementById('pageImage');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const currentPageSpan = document.getElementById('currentPage');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    function updatePageControls() {
      currentPageSpan.textContent = currentPageNum;
      prevBtn.disabled = currentPageNum <= 1;
      nextBtn.disabled = currentPageNum >= totalPages;
    }
    
    function showLoading() {
      loadingIndicator.style.display = 'flex';
      pageImage.style.display = 'none';
    }
    
    function hideLoading() {
      loadingIndicator.style.display = 'none';
      pageImage.style.display = 'block';
    }
    
    function loadPage(pageNum) {
      if (pageNum < 1 || pageNum > totalPages) return;
      
      showLoading();
      currentPageNum = pageNum;
      updatePageControls();
      
      const imageUrl = baseUrl.replace('PAGE_NUM', pageNum);
      
      const newImage = new Image();
      newImage.onload = function() {
        pageImage.src = this.src;
        hideLoading();
      };
      newImage.onerror = function() {
        hideLoading();
        pageImage.alt = 'Failed to load page ' + pageNum;
      };
      newImage.src = imageUrl;
    }
    
    function previousPage() {
      if (currentPageNum > 1) {
        loadPage(currentPageNum - 1);
      }
    }
    
    function nextPage() {
      if (currentPageNum < totalPages) {
        loadPage(currentPageNum + 1);
      }
    }
    
    document.addEventListener('keydown', function(e) {
      if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
        e.preventDefault();
        previousPage();
      } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
        e.preventDefault();
        nextPage();
      }
    });
    
    document.addEventListener('wheel', function(e) {
      if (e.ctrlKey) return;
      
      if (e.deltaY > 0) {
        nextPage();
      } else if (e.deltaY < 0) {
        previousPage();
      }
    });
    
    // Initialize
    updatePageControls();
    loadPage(1);
  </script>
</body>
