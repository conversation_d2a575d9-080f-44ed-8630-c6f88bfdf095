<!DOCTYPE html>
<html>
<head>
  <title><%= @file.display_name %></title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
      background: #f5f5f5;
    }
    
    .csv-container {
      padding: 20px;
      max-width: 100%;
      overflow-x: auto;
      background: #fff;
      min-height: 100vh;
      box-sizing: border-box;
    }
    
    .csv-table {
      width: 100%;
      border-collapse: collapse;
      background: #fff;
      margin-bottom: 1rem;
      font-size: 14px;
    }
    
    .csv-table th,
    .csv-table td {
      padding: 12px;
      text-align: left;
      border: 1px solid #e0e0e0;
      white-space: nowrap;
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .csv-table th {
      background: #f8f9fa;
      font-weight: 600;
      color: #333;
      position: sticky;
      top: 0;
      z-index: 1;
      border-bottom: 2px solid #dee2e6;
    }
    
    .csv-table tr:nth-child(even) {
      background-color: #f8f9fa;
    }
    
    .csv-table tr:hover {
      background-color: #f2f2f2;
    }
    
    .error-message {
      padding: 20px;
      text-align: center;
      color: #721c24;
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      border-radius: 4px;
      margin: 20px;
    }
    
    .file-info {
      margin-bottom: 20px;
      color: #666;
      font-size: 14px;
    }
    
    .file-info strong {
      color: #333;
    }
    
    @media (max-width: 768px) {
      .csv-container {
        padding: 10px;
      }
      
      .csv-table th,
      .csv-table td {
        padding: 8px;
        font-size: 13px;
      }
    }
  </style>
</head>
<body>
  <div class="csv-container">
    <div class="file-info">
      <strong>File:</strong> <%= @file.display_name %>
      <% if @csv_content.present? %>
        | <strong>Rows:</strong> <%= @csv_content.length %>
        | <strong>Columns:</strong> <%= @csv_content.first&.length || 0 %>
      <% end %>
    </div>
    
    <% if @csv_content.present? %>
      <div class="table-responsive">
        <table class="csv-table">
          <% @csv_content.each_with_index do |row, index| %>
            <% if index == 0 %>
              <thead>
                <tr>
                  <% row.each do |header| %>
                    <th><%= h(header) %></th>
                  <% end %>
                </tr>
              </thead>
              <tbody>
            <% else %>
              <tr>
                <% row.each do |cell| %>
                  <td><%= h(cell) %></td>
                <% end %>
              </tr>
            <% end %>
          <% end %>
          </tbody>
        </table>
      </div>
    <% else %>
      <div class="error-message">
        No data available in the CSV file.
      </div>
    <% end %>
  </div>
</body>
</html> 