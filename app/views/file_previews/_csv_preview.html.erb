<%#
  CSV Preview Partial - Renders a CSV file as an HTML Table
  
  This partial is used to display a preview of a CSV file in a tabular format.
  It shows the first 100 rows of the CSV file with proper headers and formatting.
  
  @param file [Attachment] The CSV file attachment to preview
%>
<% 
  begin
    require 'csv'
    
    csv_data = []
    CSV.parse(@file.open.read.force_encoding('UTF-8').gsub(/\x00/, ''), headers: true) do |row|
      csv_data << row
      break if csv_data.size >= 100
    end
    
    if csv_data.any?
      headers = csv_data.first.headers
%>
  <div style="width: 100%; overflow-x: auto;">
    <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
      <thead>
        <tr>
          <% headers.each do |header| %>
            <th style="padding: 8px 12px; border: 1px solid #ddd; text-align: left; background-color: #f5f5f5; font-weight: bold;"><%= header %></th>
          <% end %>
        </tr>
      </thead>
      <tbody>
        <% csv_data.each do |row| %>
          <tr>
            <% headers.each do |header| %>
              <td style="padding: 8px 12px; border: 1px solid #ddd; text-align: left;"><%= row[header] %></td>
            <% end %>
          </tr>
        <% end %>
      </tbody>
    </table>
    
    <% if csv_data.size >= 100 %>
      <div style="padding: 12px 15px; margin: 10px 0; border-radius: 3px; background-color: #e7f4ff; border-left: 4px solid #0d6efd; color: #084298;">
        <i class="icon-info" aria-hidden="true" style="margin-right: 8px;"></i>
        <span><%= t('Showing first 100 rows.') %></span> 
        <a href="<%= context_url(@context, :context_file_download_url, @file.id, download_frd: 1, verifier: params[:verifier]) %>" style="color: #0a58ca; text-decoration: underline;">
          <%= t('Download full file') %>
        </a> 
        <span><%= t('to see all data.') %></span>
      </div>
    <% end %>
  </div>
<%  else %>
  <div style="padding: 12px 15px; margin: 10px 0; border-radius: 3px; background-color: #fff3cd; border-left: 4px solid #ffc107; color: #664d03;">
    <i class="icon-warning" aria-hidden="true" style="margin-right: 8px;"></i>
    <span><%= t('This CSV file appears to be empty or contains no data.') %></span>
  </div>
<%  end %>
<%  rescue => e %>
  <div style="padding: 12px 15px; margin: 10px 0; border-radius: 3px; background-color: #f8d7da; border-left: 4px solid #dc3545; color: #842029;">
    <i class="icon-warning" aria-hidden="true" style="margin-right: 8px;"></i>
    <span><%= t('Could not generate CSV preview: %{error}', error: e.message) %></span>
  </div>
<% end %>
