<% content_for :page_title, t('Simulations') %>
<% css_bundle :simulations %>

<div class="simulations-container">
  <div class="simulations-list">
    <!-- Search and Add Section -->
    <div class="search-add-section">
      <div class="search-bar-row">
        <div class="search-container">
          <input type="text" id="simulation-search" placeholder="<%= t('Search...') %>" class="search-input">
        </div>
        <% if can_manage_simulations? %>
          <button id="open-simulation-modal" class="btn btn-primary add-btn" type="button">
            + <%= t('Add Simulation') %>
          </button>
        <% end %>
      </div>
    </div>

    <h2 class="section-title">
      <%= t('Available Simulations') %>
    </h2>

    <!-- Modal for adding simulations (only shown to managers) -->
    <% if can_manage_simulations? %>
      <div id="simulation-modal-overlay" class="modal-overlay" style="display:none;"></div>
      <div id="simulation-modal" class="modal" style="display:none;">
        <div class="modal-content">
          <span id="close-simulation-modal" class="modal-close" title="Close">&times;</span>
          <h2 style="color:#14532d; margin-bottom: 18px;"><%= t('Add Simulation') %></h2>
          <%= form_with url: course_simulations_path(@context), method: :post, local: true, html: { id: 'simulation-form', class: 'simulation-add-form' }, scope: :simulation do |f| %>
            <div>
              <%= f.label :title, t('Title') %><br>
              <%= f.text_field :title, required: true %>
            </div>
            <div>
              <%= f.label :description, t('Description') %><br>
              <%= f.text_area :description, required: true, rows: 2, class: "auto-expand-textarea" %>
            </div>
            <div>
              <%= f.label :url, t('URL') %><br>
              <%= f.url_field :url, required: true %>
            </div>
            <div class="modal-actions">
              <%= f.submit t('Add Simulation'), class: "btn btn-primary" %>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
    
    <!-- Edit simulation modal-->
    <% if can_manage_simulations? %>
      <div id="edit-simulation-modal-overlay" class="modal-overlay" style="display:none;"></div>
      <div id="edit-simulation-modal" class="modal" style="display:none;">
        <div class="modal-content">
          <span id="close-edit-simulation-modal" class="modal-close">&times;</span>
          <h2 style="color:#14532d; margin-bottom: 18px;"><%= t('Edit Simulation') %></h2>
          <%= form_with model: @simulation, url: "", method: :patch, local: true, html: { id: 'edit-simulation-form' }, scope: :simulation do |f| %>
            <input type="hidden" name="id" id="edit-simulation-id" />
            <div>
              <%= f.label :title, t('Title') %><br>
              <%= f.text_field :title, required: true, id: "edit-simulation-title" %>
            </div>
            <div>
              <%= f.label :description, t('Description') %><br>
              <%= f.text_area :description, required: true, id: "edit-simulation-description", rows: 2, class: "auto-expand-textarea" %>
            </div>
            <div>
              <%= f.label :url, t('URL') %><br>
              <%= f.url_field :url, required: true, id: "edit-simulation-url" %>
            </div>
            <div class="modal-actions">
              <button class="btn btn-primary">Save Changes</button>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
    
    <!-- Simulations display section -->
    <% if @simulations.present? %>
      <% @simulations.each do |sim| %>
        <div class="simulation-item">
          <div class="simulation-card-header">
            <a href="<%= sim[:url] %>" target="_blank" class="simulation-title-link">
              <span class="simulation-title"><%= sim[:title] %></span>
            </a>
            <div class="simulation-actions">
              <div class="dropdown">
                <button class="dropdown-toggle" aria-haspopup="true" aria-expanded="false">
                  <span class="icon-dots">&#8942;</span>
                </button>
                <div class="dropdown-menu">
                  <button class="dropdown-item edit-simulation-btn" data-id="<%= sim.id %>" data-title="<%= sim.title %>" data-description="<%= sim.description %>" data-url="<%= sim.url %>">
                    <span class="icon-edit">&#9998;</span>
                    <%= t('Edit') %>
                  </button>
                  <%= button_to course_simulation_path(@context, sim), method: :delete, data: { confirm: t('Are you sure?') }, class: "dropdown-item btn-danger" do %>
                    <span class="icon-delete">&#128465;</span> <%= t('Delete') %>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
          <div class="simulation-card-body">
            <div><span class="simulation-label">Description:</span> <%= sim[:description] %></div>
          </div>
        </div>
      <% end %>
    <% else %>
      <!-- Empty state when no simulations -->
      <div class="empty-state">
        <div class="empty-state-content">
          <div class="empty-state-icon">
            <svg width="60" height="60" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
              <!-- Globe -->
              <circle cx="60" cy="60" r="45" stroke="#14532d" stroke-width="3" fill="none"/>
              <!-- Longitude lines -->
              <path d="M 15 60 Q 60 30, 105 60 Q 60 90, 15 60" stroke="#14532d" stroke-width="2" fill="none"/>
              <path d="M 15 60 Q 60 90, 105 60 Q 60 30, 15 60" stroke="#14532d" stroke-width="2" fill="none"/>
              <!-- Latitude lines -->
              <ellipse cx="60" cy="45" rx="35" ry="8" stroke="#14532d" stroke-width="2" fill="none"/>
              <ellipse cx="60" cy="75" rx="35" ry="8" stroke="#14532d" stroke-width="2" fill="none"/>
              <line x1="15" y1="60" x2="105" y2="60" stroke="#14532d" stroke-width="2"/>
              <!-- WWW box -->
              <rect x="25" y="48" width="70" height="24" fill="white" stroke="#14532d" stroke-width="2" rx="2"/>
              <text x="60" y="65" text-anchor="middle" fill="#14532d" font-family="Arial, sans-serif" font-size="16" font-weight="bold">www.</text>
              <!-- Cursor -->
              <polygon points="85,35 85,50 90,45 95,50 100,45 95,40 100,35" fill="#14532d"/>
            </svg>
          </div>
          <div class="empty-state-text">
            <h3 class="empty-state-title">No Simulation Link</h3>
            <p class="empty-state-subtitle">
              <% if can_manage_simulations? %>
                Add a simulation link above
              <% else %>
                No simulations have been added yet
              <% end %>
            </p>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>


<script>
document.addEventListener("DOMContentLoaded", function() {
  var openBtn = document.getElementById("open-simulation-modal");
  var closeBtn = document.getElementById("close-simulation-modal");
  var modal = document.getElementById("simulation-modal");
  var overlay = document.getElementById("simulation-modal-overlay");
  var searchInput = document.getElementById("simulation-search");

  // Auto-expand textarea functionality
  function autoExpandTextarea(textarea) {
    textarea.style.height = 'auto';
    var newHeight = Math.min(textarea.scrollHeight, window.innerHeight * 0.4);
    textarea.style.height = newHeight + 'px';
  }

  // Initialize auto-expand for all textareas with the class
  document.querySelectorAll('.auto-expand-textarea').forEach(function(textarea) {
    // Set initial height
    autoExpandTextarea(textarea);
    
    // Add event listeners
    textarea.addEventListener('input', function() {
      autoExpandTextarea(this);
    });
    
    textarea.addEventListener('focus', function() {
      autoExpandTextarea(this);
    });
  });

  // Modal functionality
  if(openBtn) {
    openBtn.onclick = function() {
      modal.style.display = "block";
      overlay.style.display = "block";
      // Auto-expand textareas when modal opens
      setTimeout(function() {
        document.querySelectorAll('.auto-expand-textarea').forEach(function(textarea) {
          autoExpandTextarea(textarea);
        });
      }, 10);
    };
  }
  if(closeBtn) {
    closeBtn.onclick = function() {
      modal.style.display = "none";
      overlay.style.display = "none";
    };
  }
  if(overlay) {
    overlay.onclick = function() {
      modal.style.display = "none";
      overlay.style.display = "none";
    };
  }

  // Edit Simulation Modal functionality
  document.querySelectorAll('.edit-simulation-btn').forEach(function(btn) {
    btn.onclick = function() {
      document.getElementById('edit-simulation-id').value = btn.dataset.id;
      document.getElementById('edit-simulation-title').value = btn.dataset.title;
      document.getElementById('edit-simulation-description').value = btn.dataset.description;
      document.getElementById('edit-simulation-url').value = btn.dataset.url;
      document.getElementById('edit-simulation-form').action = '/courses/<%= @context.id %>/simulations/' + btn.dataset.id;
      document.getElementById('edit-simulation-modal').style.display = "block";
      document.getElementById('edit-simulation-modal-overlay').style.display = "block";
      
      // Auto-expand textareas when edit modal opens
      setTimeout(function() {
        document.querySelectorAll('.auto-expand-textarea').forEach(function(textarea) {
          autoExpandTextarea(textarea);
        });
      }, 10);
    };
  });
  document.getElementById('close-edit-simulation-modal').onclick = function() {
    document.getElementById('edit-simulation-modal').style.display = "none";
    document.getElementById('edit-simulation-modal-overlay').style.display = "none";
  };
  document.getElementById('edit-simulation-modal-overlay').onclick = function() {
    document.getElementById('edit-simulation-modal').style.display = "none";
    document.getElementById('edit-simulation-modal-overlay').style.display = "none";
  };

  // Search functionality
  if(searchInput) {
    searchInput.addEventListener('input', function() {
      var searchTerm = this.value.toLowerCase();
      var simulationItems = document.querySelectorAll('.simulation-item');
      
      simulationItems.forEach(function(item) {
        var titleElement = item.querySelector('.simulation-title');
        var descriptionElement = item.querySelector('.simulation-card-body');
        
        var title = titleElement ? titleElement.textContent.toLowerCase() : '';
        var description = descriptionElement ? descriptionElement.textContent.toLowerCase() : '';
        
        if (title.includes(searchTerm) || description.includes(searchTerm)) {
          item.style.display = 'block';
        } else {
          item.style.display = 'none';
        }
      });
    });
  }

  // Show/hide the dropdown menu on click
  document.querySelectorAll('.dropdown-toggle').forEach(function(btn) {
    btn.addEventListener('click', function(e) {
      e.stopPropagation();
      // Close other open dropdowns
      document.querySelectorAll('.dropdown.open').forEach(function(open) {
        if (open !== btn.parentElement) open.classList.remove('open');
      });
      btn.parentElement.classList.toggle('open');
    });
  });

  // Close dropdown when clicking outside
  document.addEventListener('click', function() {
    document.querySelectorAll('.dropdown.open').forEach(function(open) {
      open.classList.remove('open');
    });
  });
});
</script>