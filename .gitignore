# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore


# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

/.bundle/
/.yardoc

# What goes in this file:
# * local canvas configuration (db, ymls, overrides, etc.)
# * generated/cached stuff (docs, css, js, uploads, etc.)
#
# What does NOT go in this file
# * editor-related stuff
# * random scripts only used by you
# * env-specific stuff (e.g. .DS_Store)
# * pretty much any dotfile not already covered (e.g. .rvmrc)
#
# That's what .git/info/exclude and core.excludesfile are for :)
.bundle/
.env
.mutagen
.swc
.idea/
/.yardoc/
/.ruby-version
/app/views/info/styleguide.html.erb
/config/*.yml
/config/*.yml.cached
/config/credentials.yml.enc
!/config/brakeman.yml
!/config/code_ownership.yml
!/config/credentials.test.yml
!/config/crystalball.yml
/config/environments/*-local.rb
/config/locales/generated/
/config/saml/*
/config/RAILS_VERSION
/coverage/
/coverage-js/
/db/schema.rb
/db/*sql
docker-compose.override.yml
/exports/
/Gemfile.*.plugins.lock
/log/*
!/log/.keep
!/log/parallel-runtime-rspec.log
mkmf.log
node_modules
npm-debug.log
# Ignore public directories
/public/packs
/public/packs-test
/public/assets
/public/vite
/public/pdfjs
/public/doc/api/
/public/doc/api_md/
/public/javascripts/translations/*
!/public/javascripts/translations/en.json
/storybook-static/
/tmp/*
!/tmp/.keep
/vendor
yarn-error.log
dump.rdb
.eslintcache
tsconfig.tsbuildinfo

# canvas-gems
/gems/*/coverage
/gems/*/tmp
/gems/*/node_modules
/gems/plugins/*
!/gems/plugins/academic_benchmark/
!/gems/plugins/account_reports/
!/gems/plugins/moodle_importer/
!/gems/plugins/qti_exporter/
!/gems/plugins/respondus_soap_endpoint/
!/gems/plugins/simply_versioned/

# user docker compose overrides
docker-compose.local.*
!/inst-cli/docker-compose/docker-compose.local.dev.yml
# experimental api gateway config
docker-compose/api-gateway.override.yml

# sub-packages
/packages/*/node_modules
/packages/*/es
/packages/*/lib
/packages/*/coverage/
/packages/*/coverage-js/
/packages/*/tsconfig.tsbuildinfo

# pact artifacts
/reports/
/pacts/

# generated scopes
/lib/api_scope_mapper.rb

# generated scopes markdown
/doc/api/api_token_scopes.md

# generated live events docs
/doc/api/data_services/md/dynamic/*.md

# js coverage stuff
.nyc_output
coverage-jest
coverage-karma

# Ignore master key for decrypting credentials and more.
/config/master.key

# Allows devs to create their own devcontainer files
/.devcontainer/*
/!.devcontainer/.keep
/public/uploads/badges/*
!public/uploads/badges/.gitkeep