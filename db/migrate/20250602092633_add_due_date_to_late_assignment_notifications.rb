class AddDueDateToLateAssignmentNotifications < ActiveRecord::Migration[7.1]
  tag :predeploy
  def change
    add_column :late_assignment_notifications, :due_date, :date
    
    # Update existing records to have the current due date from their assignments
    # This ensures existing notifications have a due_date value
    reversible do |dir|
      dir.up do
        execute <<-SQL
          UPDATE late_assignment_notifications lan
          SET due_date = DATE(a.due_at)
          FROM assignments a
          WHERE lan.assignment_id = a.id AND a.due_at IS NOT NULL
        SQL
      end
    end
    
    # Add an index for faster lookups
    add_index :late_assignment_notifications, :due_date
  end
end
