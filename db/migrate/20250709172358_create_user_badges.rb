class CreateUserBadges < ActiveRecord::Migration[7.1]
  tag :predeploy
  
  def change
    create_table :user_badges do |t|
      t.references :user, null: false, foreign_key: true, index: true
      t.references :badge, null: false, foreign_key: true, index: true
      t.datetime :earned_at, null: false
      t.text :notes
      t.boolean :is_public, default: true
      t.timestamps
    end
    
    add_index :user_badges, :earned_at
    add_index :user_badges, :is_public
    add_index :user_badges, [:user_id, :badge_id], unique: true
  end
end