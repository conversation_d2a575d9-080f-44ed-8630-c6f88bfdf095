class CreateLateAssignmentNotifications < ActiveRecord::Migration[7.1]
  tag :predeploy
  def change
    create_table :late_assignment_notifications do |t|
      t.bigint :assignment_id, null: false
      t.bigint :user_id, null: false
      t.string :notification_type, null: false, default: 'late'
      t.datetime :sent_at, null: false

      t.timestamps
    end
    add_index :late_assignment_notifications, :assignment_id
    add_index :late_assignment_notifications, :user_id
    add_index :late_assignment_notifications, :notification_type
    # Add a unique index to prevent duplicate notifications of the same type
    add_index :late_assignment_notifications, [:assignment_id, :user_id, :notification_type], unique: true, name: 'index_late_assignment_notifications_unique'
    
    # Add foreign key constraints
    add_foreign_key :late_assignment_notifications, :assignments
    add_foreign_key :late_assignment_notifications, :users
  end
end
