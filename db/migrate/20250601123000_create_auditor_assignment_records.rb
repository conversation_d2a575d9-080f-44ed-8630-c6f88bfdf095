# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.

class CreateAuditorAssignmentRecords < ActiveRecord::Migration[7.0]
  tag :predeploy

  def up
    create_table :auditor_assignment_records, if_not_exists: true do |t|
      t.string :uuid, null: false, limit: 36
      t.string :request_id, null: false, default: 'MISSING', limit: 255
      t.references :assignment, null: false, foreign_key: true, index: { if_not_exists: true }
      t.references :user, foreign_key: true, index: { if_not_exists: true } # User can be nil for system events
      t.references :course, foreign_key: true, index: { if_not_exists: true }
      t.references :account, foreign_key: true, index: { if_not_exists: true }      
      t.string :event_type, null: false
      t.datetime :created_at, null: false

      t.index :uuid, unique: true, if_not_exists: true
      t.index :event_type, if_not_exists: true
      t.index :created_at, if_not_exists: true
      t.index [:assignment_id, :created_at], name: 'index_assignment_modifications_on_assignment_and_date', if_not_exists: true
      t.index [:user_id, :created_at], name: 'index_assignment_modifications_on_user_and_date', if_not_exists: true
      t.index [:course_id, :created_at], name: 'index_assignment_modifications_on_course_and_date', if_not_exists: true
      t.index [:account_id, :created_at], name: 'index_assignment_modifications_on_account_and_date', if_not_exists: true      
      t.index [:request_id, :created_at], name: 'index_assignment_modifications_on_request_id_and_date', if_not_exists: true
    end
  end

  def down
    drop_table :auditor_assignment_records
  end
end
