<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>SIS Import Format Documentation (Full)</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: #f8fafc;
        }
        .doc-section {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .collapsible {
            background: #e0f2fe;
            padding: 0.75rem 1rem;
            border-radius: 6px 6px 0 0;
            cursor: pointer;
            border: none;
            width: 100%;
            text-align: left;
            font-weight: bold;
            color: #0369a1;
        }
        .collapsible:hover {
            background: #bae6fd;
        }
        .content {
            display: none;
            padding: 1rem;
            border: 1px solid #cbd5e1;
            border-top: none;
            border-radius: 0 0 6px 6px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            font-size: 0.9rem;
        }
        th, td {
            border: 1px solid #e5e7eb;
            padding: 8px;
            text-align: left;
        }
        th {
            background: #f8fafc;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background: #f8fafc;
        }
        .required {
            color: #dc2626;
            font-weight: bold;
        }
        .sample {
            background: #f1f5f9;
            padding: 1rem;
            border-radius: 4px;
            border-left: 4px solid #3b82f6;
            margin: 1rem 0;
        }
        .sample pre {
            margin: 0;
            font-size: 0.85rem;
            overflow-x: auto;
        }
        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .info {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        code {
            background: #f1f5f9;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="doc-section">
        <h1 style="color: #1f2937; margin-bottom: 1rem;">📋 SIS Import Format Documentation</h1>
        <div class="info">
            <strong>Instructure Canvas SIS Integration Overview:</strong>
            <p>
                Canvas can integrate with an institution's Student Information Services (SIS) in several ways. The simplest way involves providing Canvas with several CSV files describing users, courses, and enrollments. These files can be zipped together and uploaded to the Account admin area.
            </p>
        </div>
        <div class="warning">
            <strong>⚠️ Standard CSV Rules:</strong>
            <ul>
                <li>The first row will be interpreted as a header defining the ordering of your columns. This header row is mandatory.</li>
                <li>Fields that contain a comma must be surrounded by double-quotes.</li>
                <li>Fields that contain double-quotes must also be surrounded by double-quotes, with the internal double-quotes doubled. Example: <code>"Chevy ""The Man"" Chase"</code></li>
                <li>All text should be UTF-8 encoded.</li>
                <li>All timestamps are sent and returned in ISO 8601 format: <code>YYYY-MM-DDTHH:MM:SSZ</code></li>
            </ul>
        </div>
    </div>

    <div class="doc-section">
        <h2 style="color: #0369a1;">Batch Mode, Diffing, and Stickiness</h2>
        <h3>Batch Mode</h3>
        <p>
            If the option to do a <strong>full batch update</strong> is selected in the UI, then this SIS upload is considered to be the new canonical set of data, and data from previous SIS imports that isn't present in this import will be deleted. This can be useful if the source SIS software doesn't have a way to send delete records as part of the import. This deletion is scoped to a single term, which must be specified when uploading the SIS import. Use this option with caution, as it can delete large data sets without any prompting on the individual records. Currently, this affects courses, sections and enrollments.
        </p>
        <p>
            This option will only affect data that has been involved in a previous SIS job -- either created by a previous import, or referenced by a SIS job after a SIS ID was manually added. Manually created courses with no SIS ID, for example, won't be deleted even if they don't appear in the new SIS import.
        </p>
        <p>
            During a term batch mode may be used often and if a partial file is sent, many objects can become deleted. Using <code>change_threshold=5</code> will only delete objects if the number of objects to delete is less than 5% of the objects for the term. For example: If <code>change_threshold</code> set to 5 and the term has 100 courses, and batch_mode would delete more than 5 of the courses the batch will abort before the courses are deleted. The <code>change_threshold</code> can be set to any integer between 1 and 100.
        </p>
        <p>
            <strong>change_threshold also impacts diffing mode.</strong>
        </p>
        <h3>Multi Term Batch Mode</h3>
        <p>
            Multi term batch mode is just like batch mode except against multiple terms. Multi term batch mode is run against all terms included in the same import for the batch. To use multi term batch mode you must also set a <code>change_threshold</code>. If you intend to remove all items with multi term batch mode, you can set the <code>change_threshold</code> to 100.
        </p>
        <h3>Diffing Mode</h3>
        <p>
            If your account has a SIS integration that is sending its entire data set on each import, rather than just sending what has changed, you can speed up the import process by enabling <strong>diffing mode</strong>. In diffing mode, a preprocessing step in Canvas will compare the current SIS import against the last successful SIS import with the same data set identifier, and only apply the difference between the two imports.
        </p>
        <ul>
            <li>If user A is created by import 1, and then the name is changed for user A on import 2, Canvas will apply the new information for user A.</li>
            <li>If user B is created by import 1, and then user B is omitted from import 2, Canvas will mark the user as deleted.</li>
            <li>If user C is created by import 1, and the exact same information is specified for user C in import 2, Canvas will mark that nothing has changed for that CSV row and skip looking up user C entirely. This can greatly speed up SIS imports with thousands of rows that change rarely.</li>
        </ul>
        <p>
            <strong>Note:</strong> If any SIS data was changed outside of that previous CSV import, the changes will not be noticed by the diffing code.
        </p>
        <p>
            Diffing mode is enabled by passing the <code>diffing_data_set_identifier</code> option in the "Import SIS Data" API call. This is a unique, non-changing string identifier for the series of SIS imports that will be diffed against one another. The string can contain any valid UTF-8, and be up to 128 bytes in length.
        </p>
        <p>
            When choosing a data set identifier, it's important to include any relevant details to differentiate this data set from other import data sets that may come concurrently or later. Some examples of good identifiers:
        </p>
        <ul>
            <li><code>users:fall-2015</code></li>
            <li><code>source-system-1:all-data:spring-2016</code></li>
        </ul>
        <p>
            Diffing mode by default marks objects as "deleted" when they are not included for an import, but enrollments can be marked as 'completed' or 'inactive' if the <code>diffing_drop_status</code> is passed. Likewise users removed between diffed batches can be marked as 'suspended' if the <code>diffing_user_remove_status</code> is set to suspended. If you prefer to leave removed objects alone in diffed imports, pass <code>skip_deletes=true</code> instead of either of these (this will apply to all object types, not just users and enrollments).
        </p>
        <p>
            If five consecutive SIS batches with the same diffing data set identifier exceed the change threshold, future imports will fail. You will be required to perform a remaster using the <code>diffing_remaster_data_set=true</code> option to resume imports with that data set identifier.
        </p>
        <h3>Stickiness</h3>
        <p>
            When a user makes a change to imported data in Canvas (e.g., changes a name), this change is "sticky" and is set as the new default. By default, these "sticky" changes are not overwritten on the next SIS import. This can be overridden by selecting the Override UI option, which allows Canvas to overwrite any "sticky" data updated in the Canvas UI. Otherwise, changes from an import with conflicting data would be disregarded and the existing user data would not be changed. See below for an indication of which fields have this "sticky" property.
        </p>
    </div>

    <div class="doc-section">
        <h2 style="color: #0369a1;">CSV Data Formats</h2>

        <!-- users.csv -->
        <h3>users.csv</h3>
        <div>
            <table>
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Data Type</th>
                        <th>Required</th>
                        <th>Sticky</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>user_id</td><td>text</td><td class="required">✓</td><td></td><td>A unique identifier used to reference users in the enrollments table. This identifier must not change for the user, and must be globally unique. In the user interface, this is called the SIS ID.</td></tr>
                    <tr><td>integration_id</td><td>text</td><td></td><td></td><td>A secondary unique identifier useful for more complex SIS integrations. This identifier must not change for the user, and must be globally unique.</td></tr>
                    <tr><td>login_id</td><td>text</td><td class="required">✓</td><td>✓</td><td>The name that a user will use to login to Instructure. If you have an authentication service configured (like LDAP), this will be their username from the remote system.</td></tr>
                    <tr><td>password</td><td>text</td><td></td><td></td><td>If the account is configured to use LDAP or an SSO protocol then this should not be set. Otherwise this is the password that will be used to login to Canvas along with the 'login_id' above. Setting the password will in most cases log the user out of Canvas. The password can only be set one time. If the password has been set by the user or a previous sis import, it will not be changed.</td></tr>
                    <tr><td>ssha_password</td><td>text</td><td></td><td></td><td>Instead of a plain-text password, you can pass a pre-hashed password using the SSHA password generation scheme in this field. While better than passing a plain text password, you should still encourage users to change their password after logging in for the first time.</td></tr>
                    <tr><td>authentication_provider_id</td><td>text or integer</td><td></td><td></td><td>The authentication provider this login is associated with. Can be the integer ID or the type of the provider.</td></tr>
                    <tr><td>first_name</td><td>text</td><td></td><td>✓</td><td>Given name of the user. If present, used to construct full_name and/or sortable_name.</td></tr>
                    <tr><td>last_name</td><td>text</td><td></td><td>✓</td><td>Last name of the user. If present, used to construct full_name and/or sortable_name.</td></tr>
                    <tr><td>full_name</td><td>text</td><td></td><td>✓</td><td>Full name of the user. Omit first_name and last_name if this is provided.</td></tr>
                    <tr><td>sortable_name</td><td>text</td><td></td><td>✓</td><td>Sortable name of the user. Normally inferred from the user's name, but you can customize it here.</td></tr>
                    <tr><td>short_name</td><td>text</td><td></td><td>✓</td><td>Display name of the user. Normally inferred from the user's name, but you can customize it here.</td></tr>
                    <tr><td>email</td><td>text</td><td></td><td></td><td>The email address of the user. Recommended to omit this field over using fake email addresses for testing.</td></tr>
                    <tr><td>department</td><td>text</td><td></td><td></td><td>User's department or major (e.g., "Mathematics", "Computer Science").</td></tr>
                    <tr><td>role</td><td>text</td><td></td><td></td><td>User's primary role (e.g., "student", "teacher", "staff", "admin").</td></tr>
                    <tr><td>gender</td><td>text</td><td></td><td></td><td>User's gender (e.g., "male", "female").</td></tr>
                    <tr><td>standing_year</td><td>text or integer</td><td></td><td></td><td>User's academic standing or year (e.g., "freshman", "sophomore", "junior", "senior", or 1, 2, 3, 4).</td></tr>
                    <tr><td>pronouns</td><td>text</td><td></td><td>✓</td><td>User's preferred pronouns. Can pass "&lt;delete&gt;" to remove the pronoun from the user. Ignored unless the "Enable Personal Pronouns" account setting is enabled.</td></tr>
                    <tr><td>declared_user_type</td><td>enum</td><td></td><td></td><td>User's declared user type. Can be administrative, observer, staff, student, student_other, or teacher. Can pass "&lt;delete&gt;" to remove.</td></tr>
                    <tr><td>canvas_password_notification</td><td>boolean</td><td></td><td></td><td>Defaults to false. When true, user is notified for password setup if the authentication_provider_id is "canvas".</td></tr>
                    <tr><td>home_account</td><td>boolean</td><td></td><td></td><td>Setting this to true will create a new user in the target account for the SIS import and merge in another existing user from another account within the consortium with a matching integration_id. Ignored unless the target account is associated with an auto-merge consortium.</td></tr>
                    <tr><td>status</td><td>enum</td><td class="required">✓</td><td>✓</td><td>active, suspended, deleted</td></tr>
                </tbody>
            </table>
            <div class="sample">
                <strong>Sample users.csv:</strong>
                <pre>user_id,login_id,authentication_provider_id,password,first_name,last_name,short_name,email,department,role,gender,standing_year,status
01103,bsmith01,,,Bob,Smith,Bobby Smith,<EMAIL>,Mathematics,student,male,senior,active
13834,jdoe03,google,,John,Doe,,<EMAIL>,English,student,female,junior,active
13aa3,psue01,7,,Peggy,Sue,,<EMAIL>,Computer Science,teacher,female,,active</pre>
            </div>
        </div>

        <!-- accounts.csv -->
        <h3>accounts.csv</h3>
        <div>
            <table>
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Data Type</th>
                        <th>Required</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>account_id</td><td>text</td><td class="required">✓</td><td>Unique identifier for the account (SIS ID)</td></tr>
                    <tr><td>parent_account_id</td><td>text</td><td class="required">✓</td><td>Parent account identifier. Blank = root account.</td></tr>
                    <tr><td>name</td><td>text</td><td class="required">✓</td><td>Account name</td></tr>
                    <tr><td>status</td><td>enum</td><td class="required">✓</td><td>active, deleted</td></tr>
                    <tr><td>integration_id</td><td>text</td><td></td><td>Secondary unique identifier for complex integrations</td></tr>
                </tbody>
            </table>
            <div class="sample">
                <strong>Sample accounts.csv:</strong>
                <pre>account_id,parent_account_id,name,status
A001,,College of Arts and Sciences,active
A002,A001,Department of Mathematics,active
A003,A001,Department of English,active</pre>
            </div>
        </div>

        <!-- terms.csv -->
        <h3>terms.csv</h3>
        <div>
            <table>
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Data Type</th>
                        <th>Required</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>term_id</td><td>text</td><td class="required">✓</td><td>Unique identifier for the term (SIS ID)</td></tr>
                    <tr><td>name</td><td>text</td><td class="required">✓</td><td>Term name (e.g., "Fall 2024")</td></tr>
                    <tr><td>status</td><td>enum</td><td class="required">✓</td><td>active, deleted</td></tr>
                    <tr><td>start_date</td><td>date</td><td></td><td>Term start date (ISO 8601)</td></tr>
                    <tr><td>end_date</td><td>date</td><td></td><td>Term end date (ISO 8601)</td></tr>
                    <tr><td>integration_id</td><td>text</td><td></td><td>Secondary unique identifier</td></tr>
                </tbody>
            </table>
            <div class="sample">
                <strong>Sample terms.csv:</strong>
                <pre>term_id,name,status,start_date,end_date
TERM001,Fall 2024,active,2024-08-15T00:00:00Z,2024-12-15T23:59:59Z
TERM002,Spring 2025,active,2025-01-15T00:00:00Z,2025-05-15T23:59:59Z</pre>
            </div>
        </div>

        <!-- courses.csv -->
        <h3>courses.csv</h3>
        <div>
            <table>
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Data Type</th>
                        <th>Required</th>
                        <th>Sticky</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>course_id</td><td>text</td><td class="required">✓</td><td></td><td>Unique identifier for the course (SIS ID). Must not change and be globally unique.</td></tr>
                    <tr><td>short_name</td><td>text</td><td class="required">✓</td><td>✓</td><td>Short name for the course (e.g., "MATH101")</td></tr>
                    <tr><td>long_name</td><td>text</td><td class="required">✓</td><td>✓</td><td>Full descriptive name for the course</td></tr>
                    <tr><td>account_id</td><td>text</td><td></td><td>✓</td><td>Account identifier from accounts.csv. Uses root account if not specified.</td></tr>
                    <tr><td>term_id</td><td>text</td><td></td><td>✓</td><td>Term identifier from terms.csv. Uses default term if not specified.</td></tr>
                    <tr><td>status</td><td>enum</td><td class="required">✓</td><td>✓</td><td>active, deleted, completed, published</td></tr>
                    <tr><td>start_date</td><td>date</td><td></td><td>✓</td><td>Course start date (ISO 8601). Overrides term start date.</td></tr>
                    <tr><td>end_date</td><td>date</td><td></td><td>✓</td><td>Course end date (ISO 8601). Overrides term end date.</td></tr>
                </tbody>
            </table>
            <div class="sample">
                <strong>Sample courses.csv:</strong>
                <pre>course_id,short_name,long_name,account_id,term_id,status
COURSE001,MATH101,Introduction to Mathematics,ACCT001,TERM001,active
COURSE002,ENG101,English Literature,ACCT001,TERM001,active</pre>
            </div>
        </div>

        <!-- sections.csv -->
        <h3>sections.csv</h3>
        <div>
            <table>
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Data Type</th>
                        <th>Required</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>section_id</td><td>text</td><td class="required">✓</td><td>Unique identifier for the section (SIS ID)</td></tr>
                    <tr><td>course_id</td><td>text</td><td class="required">✓</td><td>Course identifier from courses.csv</td></tr>
                    <tr><td>name</td><td>text</td><td class="required">✓</td><td>Section name</td></tr>
                    <tr><td>status</td><td>enum</td><td class="required">✓</td><td>active, deleted</td></tr>
                    <tr><td>start_date</td><td>date</td><td></td><td>Section start date (overrides course/term dates)</td></tr>
                    <tr><td>end_date</td><td>date</td><td></td><td>Section end date (overrides course/term dates)</td></tr>
                </tbody>
            </table>
            <div class="sample">
                <strong>Sample sections.csv:</strong>
                <pre>section_id,course_id,name,status
SEC001,COURSE001,Section A,active
SEC002,COURSE001,Section B,active
SEC003,COURSE002,Morning Section,active</pre>
            </div>
        </div>

        <!-- enrollments.csv -->
        <h3>enrollments.csv</h3>
        <div>
            <table>
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Data Type</th>
                        <th>Required</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>course_id</td><td>text</td><td class="required">✓*</td><td>Course identifier from courses.csv</td></tr>
                    <tr><td>user_id</td><td>text</td><td class="required">✓*</td><td>User identifier from users.csv</td></tr>
                    <tr><td>role</td><td>text</td><td class="required">✓*</td><td>student, teacher, ta, observer, designer, or custom role</td></tr>
                    <tr><td>section_id</td><td>text</td><td class="required">✓*</td><td>Section identifier from sections.csv. Uses default section if not specified.</td></tr>
                    <tr><td>status</td><td>enum</td><td class="required">✓</td><td>active, deleted, completed, inactive</td></tr>
                    <tr><td>start_date</td><td>date</td><td></td><td>Enrollment start date (ISO 8601)</td></tr>
                    <tr><td>end_date</td><td>date</td><td></td><td>Enrollment end date (ISO 8601)</td></tr>
                </tbody>
            </table>
            <div class="sample">
                <strong>Sample enrollments.csv:</strong>
                <pre>course_id,user_id,role,section_id,status
COURSE001,student001,student,SEC001,active
COURSE001,teacher001,teacher,SEC001,active
COURSE002,student001,student,SEC002,active</pre>
            </div>
        </div>

        <!-- group_categories.csv -->
        <h3>group_categories.csv</h3>
        <div>
            <table>
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Data Type</th>
                        <th>Required</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>group_category_id</td><td>text</td><td class="required">✓</td><td>Unique identifier for the group category</td></tr>
                    <tr><td>account_id</td><td>text</td><td></td><td>Account identifier (optional if course_id provided)</td></tr>
                    <tr><td>course_id</td><td>text</td><td></td><td>Course identifier (optional if account_id provided)</td></tr>
                    <tr><td>category_name</td><td>text</td><td class="required">✓</td><td>Name of the group category</td></tr>
                    <tr><td>status</td><td>enum</td><td class="required">✓</td><td>active, deleted</td></tr>
                </tbody>
            </table>
            <div class="sample">
                <strong>Sample group_categories.csv:</strong>
                <pre>group_category_id,course_id,category_name,status
GC001,COURSE001,Project Groups,active</pre>
            </div>
        </div>

        <!-- groups.csv -->
        <h3>groups.csv</h3>
        <div>
            <table>
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Data Type</th>
                        <th>Required</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>group_id</td><td>text</td><td class="required">✓</td><td>Unique identifier for the group</td></tr>
                    <tr><td>group_category_id</td><td>text</td><td></td><td>Group category identifier</td></tr>
                    <tr><td>account_id</td><td>text</td><td></td><td>Account identifier</td></tr>
                    <tr><td>course_id</td><td>text</td><td></td><td>Course identifier</td></tr>
                    <tr><td>name</td><td>text</td><td class="required">✓</td><td>Group name</td></tr>
                    <tr><td>status</td><td>enum</td><td class="required">✓</td><td>available, deleted</td></tr>
                </tbody>
            </table>
            <div class="sample">
                <strong>Sample groups.csv:</strong>
                <pre>group_id,group_category_id,course_id,name,status
GROUP001,GC001,COURSE001,Team Alpha,available
GROUP002,GC001,COURSE001,Team Beta,available</pre>
            </div>
        </div>

        <!-- admins.csv -->
        <h3>admins.csv</h3>
        <div>
            <table>
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Data Type</th>
                        <th>Required</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>user_id</td><td>text</td><td class="required">✓</td><td>User identifier from users.csv</td></tr>
                    <tr><td>account_id</td><td>text</td><td class="required">✓</td><td>Account identifier from accounts.csv</td></tr>
                    <tr><td>role</td><td>text</td><td class="required">✓*</td><td>AccountAdmin or custom role (case sensitive)</td></tr>
                    <tr><td>role_id</td><td>text</td><td class="required">✓*</td><td>Role ID (alternative to role field)</td></tr>
                    <tr><td>status</td><td>enum</td><td class="required">✓</td><td>active, deleted</td></tr>
                </tbody>
            </table>
            <div class="sample">
                <strong>Sample admins.csv:</strong>
                <pre>user_id,account_id,role,status
admin001,A001,AccountAdmin,active
admin002,A002,CustomAdmin,active</pre>
            </div>
        </div>

        <!-- user_observers.csv -->
        <h3>user_observers.csv</h3>
        <div>
            <table>
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Data Type</th>
                        <th>Required</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>observer_id</td><td>text</td><td class="required">✓</td><td>User identifier from users.csv for the observing user</td></tr>
                    <tr><td>student_id</td><td>text</td><td class="required">✓</td><td>User identifier from users.csv for the student user</td></tr>
                    <tr><td>status</td><td>enum</td><td class="required">✓</td><td>active, deleted</td></tr>
                </tbody>
            </table>
            <div class="sample">
                <strong>Sample user_observers.csv:</strong>
                <pre>observer_id,student_id,status
parent001,student001,active
parent002,student002,active</pre>
            </div>
        </div>

        <!-- xlists.csv -->
        <h3>xlists.csv</h3>
        <div>
            <table>
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Data Type</th>
                        <th>Required</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>xlist_course_id</td><td>text</td><td class="required">✓</td><td>Course identifier to cross-list sections into</td></tr>
                    <tr><td>section_id</td><td>text</td><td class="required">✓</td><td>Section identifier from sections.csv to be moved</td></tr>
                    <tr><td>status</td><td>enum</td><td class="required">✓</td><td>active, deleted</td></tr>
                </tbody>
            </table>
            <div class="sample">
                <strong>Sample xlists.csv:</strong>
                <pre>xlist_course_id,section_id,status
COURSE001,SEC001,active
COURSE001,SEC002,active</pre>
            </div>
        </div>

        <!-- change_sis_id.csv -->
        <h3>change_sis_id.csv</h3>
        <div>
            <table>
                <thead>
                    <tr>
                        <th>Field Name</th>
                        <th>Data Type</th>
                        <th>Required</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>old_id</td><td>text</td><td class="required">✓*</td><td>Current SIS ID of the object to be changed</td></tr>
                    <tr><td>new_id</td><td>text</td><td class="required">✓*</td><td>Desired new SIS ID (must be unique)</td></tr>
                    <tr><td>old_integration_id</td><td>text</td><td class="required">✓*</td><td>Current integration ID to be changed</td></tr>
                    <tr><td>new_integration_id</td><td>text</td><td class="required">✓*</td><td>Desired new integration ID or "&lt;delete&gt;" to remove</td></tr>
                    <tr><td>type</td><td>text</td><td class="required">✓</td><td>account, term, course, section, group, group_category, user</td></tr>
                </tbody>
            </table>
            <div class="sample">
                <strong>Sample change_sis_id.csv:</strong>
                <pre>old_id,new_id,type
USER001,STU001,user
COURSE_OLD,COURSE_NEW,course</pre>
            </div>
        </div>
    </div>

    <div class="doc-section">
        <h2 style="color: #dc2626; margin-bottom: 1rem;">⚠️ Important Warnings & Best Practices</h2>
        <div class="warning">
            <h4>Before You Import:</h4>
            <ul>
                <li><strong>Test with small datasets first</strong> - Import a few records to verify formatting</li>
                <li><strong>Backup existing data</strong> - SIS imports can overwrite existing information</li>
                <li><strong>Use consistent IDs</strong> - Once set, SIS IDs should never change</li>
                <li><strong>Validate CSV format</strong> - Ensure proper encoding (UTF-8) and formatting</li>
            </ul>
        </div>
        <div class="info">
            <h4>Status Field Guidelines:</h4>
            <ul>
                <li><strong>active</strong> - Normal operation, user/course is available</li>
                <li><strong>deleted</strong> - Removes the object from the system</li>
                <li><strong>suspended</strong> (users) - Prevents login but preserves enrollments</li>
                <li><strong>completed</strong> (courses/enrollments) - Read-only access</li>
                <li><strong>inactive</strong> (enrollments) - User appears in roster but cannot participate</li>
            </ul>
        </div>
    </div>

    <script>
        // Make all collapsible sections work
        document.querySelectorAll('.collapsible').forEach(button => {
            button.addEventListener('click', function() {
                this.classList.toggle('active');
                const content = this.nextElementSibling;
                if (content.style.display === 'block') {
                    content.style.display = 'none';
                } else {
                    content.style.display = 'block';
                }
            });
        });
    </script>
</body>
</html>