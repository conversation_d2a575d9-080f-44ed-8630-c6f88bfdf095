# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

require_relative "../spec_helper"
require_relative "../apis/api_spec_helper"

describe AssignmentAuditApiController do
  include Api
  include Api::V1::AssignmentAuditEvent

  before do
    @request.host = "www.example.com"
    @account = Account.default
    @sub_account = Account.create!(parent_account: @account)
    @sub_sub_account = Account.create!(parent_account: @sub_account)

    @admin = account_admin_user
    @user = user_factory
    @course = course_factory(account: @sub_sub_account, active_all: true)
    @assignment = @course.assignments.create!(title: "Test Assignment")
    @student = student_in_course(course: @course, active_all: true).user
    @teacher = teacher_in_course(course: @course, active_all: true).user
    user_session(@admin)
  end

  describe "query by account" do
    before do
      @events = []
      @events << Auditors::AssignmentNotification.record(@assignment, @teacher, "notification_sent", { recipients: [@student.id] })
      @events << Auditors::AssignmentDueDate.record(@assignment, @teacher, "due_date_changed", { old_date: 1.day.ago, new_date: Time.now })
    end

    it "returns events at the account level" do
      json = api_call(:get,
                      "/api/v1/audit/assignment/accounts/#{@account.id}",
                      { controller: "assignment_audit_api",
                        action: "for_account",
                        account_id: @account.id.to_s,
                        format: "json" })
      expect(json["events"].size).to eq 2
      expect(json["linked"]["assignments"].size).to eq 1
      expect(json["linked"]["courses"].size).to eq 1
      expect(json["linked"]["users"].size).to eq 2
    end

    it "returns events at the sub account level" do
      json = api_call(:get,
                      "/api/v1/audit/assignment/accounts/#{@sub_account.id}",
                      { controller: "assignment_audit_api",
                        action: "for_account",
                        account_id: @sub_account.id.to_s,
                        format: "json" })
      expect(json["events"].size).to eq 2
    end

    it "returns events at the sub_sub_account level" do
      json = api_call(:get,
                      "/api/v1/audit/assignment/accounts/#{@sub_sub_account.id}",
                      { controller: "assignment_audit_api",
                        action: "for_account",
                        account_id: @sub_sub_account.id.to_s,
                        format: "json" })
      expect(json["events"].size).to eq 2
    end
  end

  describe "query by course" do
    before do
      @events = []
      @events << Auditors::AssignmentNotification.record(@assignment, @teacher, "notification_sent", { recipients: [@student.id] })
      @events << Auditors::AssignmentDueDate.record(@assignment, @teacher, "due_date_changed", { old_date: 1.day.ago, new_date: Time.now })
      
      # Create a second course with a similar name for testing course lookup by name
      @course2 = course_factory(account: @sub_sub_account, active_all: true)
      @course2.name = "#{@course.name} - Section 2"
      @course2.save!
      @assignment2 = @course2.assignments.create!(title: "Test Assignment 2")
      @events << Auditors::AssignmentNotification.record(@assignment2, @teacher, "notification_sent", { recipients: [@student.id] })
    end

    it "returns events at the course level" do
      json = api_call(:get,
                       "/api/v1/audit/assignment/courses/#{@course.id}",
                       { controller: "assignment_audit_api",
                         action: "for_course",
                         course_id: @course.id.to_s,
                         format: "json" })
      expect(json["events"].size).to eq 2
      expect(json["linked"]["assignments"].size).to eq 1
      expect(json["linked"]["courses"].size).to eq 1
      expect(json["linked"]["users"].size).to eq 2
    end
    
    it "finds course by name" do
      json = api_call(:get,
                      "/api/v1/audit/assignment/courses/#{@course.name}",
                      { controller: "assignment_audit_api",
                        action: "for_course",
                        course_id: @course.name,
                        format: "json" })
      expect(json["events"].size).to eq 2
      expect(json["linked"]["courses"].first["id"]).to eq @course.id.to_s
    end

    it "finds course by partial name match" do
      # Test with the second course that has a similar name
      json = api_call(:get,
                      "/api/v1/audit/assignment/courses/#{@course2.name}",
                      { controller: "assignment_audit_api",
                        action: "for_course",
                        course_id: @course2.name,
                        format: "json" })
      expect(json["events"].size).to eq 1
      expect(json["linked"]["courses"].first["id"]).to eq @course2.id.to_s
    end

    it "returns 404 for non-existent course" do
      expect do
        api_call(:get,
                "/api/v1/audit/assignment/courses/Non-existent Course",
                { controller: "assignment_audit_api",
                  action: "for_course",
                  course_id: "Non-existent Course",
                  format: "json" })
      end.to raise_error(ActiveRecord::RecordNotFound)
    end
    
    it "filters events by event_type for course" do
      json = api_call(:get,
                      "/api/v1/audit/assignment/courses/#{@course.id}",
                      { controller: "assignment_audit_api",
                        action: "for_course",
                        course_id: @course.id.to_s,
                        event_type: "notification_sent",
                        format: "json" })
      expect(json["events"].size).to eq 1
      expect(json["events"][0]["event_type"]).to eq "notification_sent"
      
      # Test with a different event type
      json = api_call(:get,
                      "/api/v1/audit/assignment/courses/#{@course.id}",
                      { controller: "assignment_audit_api",
                        action: "for_course",
                        course_id: @course.id.to_s,
                        event_type: "due_date_changed",
                        format: "json" })
      expect(json["events"].size).to eq 1
      expect(json["events"][0]["event_type"]).to eq "due_date_changed"
    end
    
    it "combines course lookup by name and event_type filtering" do
      json = api_call(:get,
                      "/api/v1/audit/assignment/courses/#{@course.name}",
                      { controller: "assignment_audit_api",
                        action: "for_course",
                        course_id: @course.name,
                        event_type: "notification_sent",
                        format: "json" })
      expect(json["events"].size).to eq 1
      expect(json["events"][0]["event_type"]).to eq "notification_sent"
      expect(json["linked"]["courses"].first["id"]).to eq @course.id.to_s
    end
  end

  describe "query by assignment" do
    before do
      @events = []
      @events << Auditors::AssignmentNotification.record(@assignment, @teacher, "notification_sent", { recipients: [@student.id] })
      @events << Auditors::AssignmentDueDate.record(@assignment, @teacher, "due_date_changed", { old_date: 1.day.ago, new_date: Time.now })
    end

    it "returns events for the assignment" do
      json = api_call(:get,
                       "/api/v1/audit/assignment/assignments/#{@assignment.id}",
                       { controller: "assignment_audit_api",
                         action: "for_assignment",
                         assignment_id: @assignment.id.to_s,
                         format: "json" })
      expect(json["events"].size).to eq 2
      expect(json["linked"]["assignments"].size).to eq 1
      expect(json["linked"]["courses"].size).to eq 1
      expect(json["linked"]["users"].size).to eq 2
    end
    
    it "filters events by event_type" do
      json = api_call(:get,
                      "/api/v1/audit/assignment/assignments/#{@assignment.id}",
                      { controller: "assignment_audit_api",
                        action: "for_assignment",
                        assignment_id: @assignment.id.to_s,
                        event_type: "notification_sent",
                        format: "json" })
      expect(json["events"].size).to eq 1
      expect(json["events"][0]["event_type"]).to eq "notification_sent"
      
      json = api_call(:get,
                      "/api/v1/audit/assignment/assignments/#{@assignment.id}",
                      { controller: "assignment_audit_api",
                        action: "for_assignment",
                        assignment_id: @assignment.id.to_s,
                        event_type: "due_date_changed",
                        format: "json" })
      expect(json["events"].size).to eq 1
      expect(json["events"][0]["event_type"]).to eq "due_date_changed"
    end
  end

  describe "query by user" do
    before do
      @events = []
      @events << Auditors::AssignmentNotification.record(@assignment, @teacher, "notification_sent", { recipients: [@student.id] })
      @events << Auditors::AssignmentDueDate.record(@assignment, @teacher, "due_date_changed", { old_date: 1.day.ago, new_date: Time.now })
    end

    it "returns events for the user" do
      json = api_call(:get,
                      "/api/v1/audit/assignment/users/#{@teacher.id}",
                      { controller: "assignment_audit_api",
                        action: "for_user",
                        user_id: @teacher.id.to_s,
                        format: "json" })
      expect(json["events"].size).to eq 2
      expect(json["linked"]["assignments"].size).to eq 1
      expect(json["linked"]["courses"].size).to eq 1
      expect(json["linked"]["users"].size).to eq 2
    end
  end

  describe "date filtering" do
    before do
      @yesterday = 1.day.ago
      @today = Time.now
      @tomorrow = 1.day.from_now
      
      # Create events with different timestamps
      Timecop.freeze(@yesterday) do
        @past_event = Auditors::AssignmentNotification.record(@assignment, @teacher, "notification_sent", { recipients: [@student.id] })
      end
      
      Timecop.freeze(@tomorrow) do
        @future_event = Auditors::AssignmentDueDate.record(@assignment, @teacher, "due_date_changed", { old_date: @today, new_date: @tomorrow })
      end
    end
    
    it "filters events by start_time" do
      json = api_call(:get,
                      "/api/v1/audit/assignment/assignments/#{@assignment.id}",
                      { controller: "assignment_audit_api",
                        action: "for_assignment",
                        assignment_id: @assignment.id.to_s,
                        start_time: @today.iso8601,
                        format: "json" })
      expect(json["events"].size).to eq 1
      expect(json["events"][0]["id"]).to eq @future_event.id.to_s
    end
    
    it "filters events by end_time" do
      json = api_call(:get,
                      "/api/v1/audit/assignment/assignments/#{@assignment.id}",
                      { controller: "assignment_audit_api",
                        action: "for_assignment",
                        assignment_id: @assignment.id.to_s,
                        end_time: @today.iso8601,
                        format: "json" })
      expect(json["events"].size).to eq 1
      expect(json["events"][0]["id"]).to eq @past_event.id.to_s
    end
    
    it "filters events by date range" do
      # Create a third event in the middle
      middle_event = Auditors::AssignmentNotification.record(@assignment, @teacher, "notification_sent", { recipients: [@student.id] })
      
      json = api_call(:get,
                      "/api/v1/audit/assignment/assignments/#{@assignment.id}",
                      { controller: "assignment_audit_api",
                        action: "for_assignment",
                        assignment_id: @assignment.id.to_s,
                        start_time: @yesterday.iso8601,
                        end_time: @tomorrow.iso8601,
                        format: "json" })
      expect(json["events"].size).to eq 3
      
      # Narrow the range to exclude the past and future events
      narrow_start = @yesterday + 12.hours
      narrow_end = @tomorrow - 12.hours
      json = api_call(:get,
                      "/api/v1/audit/assignment/assignments/#{@assignment.id}",
                      { controller: "assignment_audit_api",
                        action: "for_assignment",
                        assignment_id: @assignment.id.to_s,
                        start_time: narrow_start.iso8601,
                        end_time: narrow_end.iso8601,
                        format: "json" })
      expect(json["events"].size).to eq 1
      expect(json["events"][0]["id"]).to eq middle_event.id.to_s
    end
  end
end
