# frozen_string_literal: true

#
# Copyright (C) 2024 - present Instructure, Inc.
#
# Simple test for feedback preset functionality
#

require 'spec_helper'

describe GradebooksController, type: :controller do
  let(:course) { course_factory(active_all: true) }
  let(:teacher) { teacher_in_course(course: course, active_all: true).user }
  let(:assignment) { assignment_model(course: course) }

  before do
    user_session(teacher)
  end

  describe "feedback preset methods" do
    before do
      controller.instance_variable_set(:@context, course)
      controller.instance_variable_set(:@assignment, assignment)
      allow(controller).to receive(:current_user).and_return(teacher)
    end

    it "responds to feedback_presets_enabled?" do
      expect(controller).to respond_to(:feedback_presets_enabled?)
    end

    it "returns true for feedback_presets_enabled?" do
      expect(controller.send(:feedback_presets_enabled?)).to be_truthy
    end

    it "responds to feedback_presets_for_course" do
      expect(controller).to respond_to(:feedback_presets_for_course)
    end

    it "returns default presets array" do
      presets = controller.send(:feedback_presets_for_course)
      
      expect(presets).to be_an(Array)
      expect(presets.length).to be > 0
      expect(presets.first).to have_key(:text)
      expect(presets.first).to have_key(:emoji)
    end

    it "includes expected default preset texts" do
      presets = controller.send(:feedback_presets_for_course)
      preset_texts = presets.map { |p| p[:text] }
      
      expect(preset_texts).to include("Nice try")
      expect(preset_texts).to include("Good job") 
      expect(preset_texts).to include("Konting push pa")
    end

    it "includes emojis in presets" do
      presets = controller.send(:feedback_presets_for_course)
      
      expect(presets.first[:emoji]).to be_present
      expect(presets.first[:emoji]).to match(/👍|✅|💪/)
    end
  end
end
