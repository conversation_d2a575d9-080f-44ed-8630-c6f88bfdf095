# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

require_relative '../spec_helper'

describe 'Syllabus Content Restriction Integration', type: :request do
  before do
    # Use Canvas test helpers to properly set up course and users
    course_with_student(active_all: true)
    @teacher = user_factory(active_all: true)
    @course.enroll_teacher(@teacher, enrollment_state: 'active')
  end

  context 'when syllabus reading is required' do
    before do
      @course.syllabus_body = 'This is the course syllabus content that students must read.'
      @course.save!
    end

    context 'student has not read syllabus' do
      it 'shows restricted content message for assignments page' do
        user_session(@student)
        get "/courses/#{@course.id}/assignments"

        puts "Response status: #{response.status}"
        puts "Response headers: #{response.headers['Location']}" if response.headers['Location']

        # Follow the redirect manually
        if response.status == 302
          redirect_url = response.headers['Location']
          puts "Following redirect to: #{redirect_url}"
          get redirect_url
          puts "After redirect - Response status: #{response.status}"
          puts "Response body: #{response.body[0..500]}" if response.body
        end

        expect(response).to be_successful
        expect(response.body).to include('Please read and complete the course syllabus')
        expect(response.body).to include('Read Syllabus')
        expect(response.body).not_to include('assignment') # Normal assignments content should not be shown
      end

      it 'shows restricted content message for discussions page' do
        user_session(student)
        get "/courses/#{course.id}/discussion_topics"

        expect(response).to be_successful
        expect(response.body).to include('Please read and complete the course syllabus')
        expect(response.body).to include('Read Syllabus')
      end

      it 'allows access to syllabus page' do
        user_session(student)
        get "/courses/#{course.id}/assignments/syllabus"

        expect(response).to be_successful
        expect(response.body).to include('This is the course syllabus content')
        expect(response.body).not_to include('Please read and complete the course syllabus')
      end

      it 'allows teachers to access all content regardless of syllabus reading' do
        user_session(teacher)
        get "/courses/#{course.id}/assignments"

        expect(response).to be_successful
        expect(response.body).not_to include('Please read and complete the course syllabus')
        # Teachers should see normal content
      end
    end

    context 'student has read syllabus' do
      before do
        student_enrollment.mark_syllabus_as_read!
      end

      it 'allows access to assignments page' do
        user_session(student)
        get "/courses/#{course.id}/assignments"

        expect(response).to be_successful
        expect(response.body).not_to include('Please read and complete the course syllabus')
        # Should show normal assignments content
      end

      it 'allows access to discussions page' do
        user_session(student)
        get "/courses/#{course.id}/discussion_topics"

        expect(response).to be_successful
        expect(response.body).not_to include('Please read and complete the course syllabus')
        # Should show normal discussions content
      end
    end

    context 'syllabus is updated after student has read it' do
      before do
        student_enrollment.mark_syllabus_as_read!
        # Simulate syllabus update
        course.syllabus_body = 'Updated syllabus content that requires re-reading.'
        course.save!
      end

      it 'restricts access again until student re-reads updated syllabus' do
        user_session(student)
        get "/courses/#{course.id}/assignments"

        expect(response).to be_successful
        expect(response.body).to include('Please read and complete the course syllabus')
        expect(response.body).to include('Read Syllabus')
      end
    end
  end

  context 'when syllabus reading is not required' do
    before do
      course.syllabus_body = nil # No syllabus content
      course.save!
    end

    it 'allows students to access all content without restriction' do
      user_session(student)
      get "/courses/#{course.id}/assignments"

      expect(response).to be_successful
      expect(response.body).not_to include('Please read and complete the course syllabus')
      # Should show normal assignments content
    end
  end
end
