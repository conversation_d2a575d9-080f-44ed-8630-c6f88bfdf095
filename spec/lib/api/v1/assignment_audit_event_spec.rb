# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

require_relative "../../../spec_helper"

describe Api::V1::AssignmentAuditEvent do
  include Api::V1::AssignmentAuditEvent

  before do
    @request_id = SecureRandom.uuid
    @domain_root_account = Account.default

    @account = Account.default
    course_with_teacher(account: @account)
    student_in_course(course: @course)
    @assignment = @course.assignments.create!(title: "Test Assignment")
  end

  describe "#assignment_audit_event_json" do
    context "for notification events" do
      before do
        @event = Auditors::AssignmentNotification.record(@assignment, @teacher, "notification_sent", { recipients: [@student.id] })
      end

      it "formats notification events properly" do
        json = assignment_audit_event_json(@event, @teacher, @session)
        expect(json["id"]).to eq @event.id
        expect(json["assignment_id"]).to eq @assignment.id
        expect(json["user_id"]).to eq @teacher.id
        expect(json["event_type"]).to eq "notification_sent"
        expect(json["event_data"]["recipients"]).to eq [@student.id]
        expect(json["created_at"]).not_to be_nil
      end
    end

    context "for due date events" do
      before do
        @old_date = 1.day.ago
        @new_date = Time.now
        @event = Auditors::AssignmentDueDate.record(@assignment, @teacher, "due_date_changed", { old_date: @old_date, new_date: @new_date })
      end

      it "formats due date events properly" do
        json = assignment_audit_event_json(@event, @teacher, @session)
        expect(json["id"]).to eq @event.id
        expect(json["assignment_id"]).to eq @assignment.id
        expect(json["user_id"]).to eq @teacher.id
        expect(json["event_type"]).to eq "due_date_changed"
        expect(json["event_data"]["old_date"]).to eq @old_date.iso8601
        expect(json["event_data"]["new_date"]).to eq @new_date.iso8601
        expect(json["created_at"]).not_to be_nil
      end
    end
  end

  describe "#assignment_audit_events_compound_json" do
    before do
      @events = []
      @events << Auditors::AssignmentNotification.record(@assignment, @teacher, "notification_sent", { recipients: [@student.id] })
      @events << Auditors::AssignmentDueDate.record(@assignment, @teacher, "due_date_changed", { old_date: 1.day.ago, new_date: Time.now })
    end

    it "returns a compound json structure with linked objects" do
      json = assignment_audit_events_compound_json(@events, @teacher, @session)
      expect(json[:events].size).to eq 2
      expect(json[:linked][:assignments].size).to eq 1
      expect(json[:linked][:courses].size).to eq 1
      expect(json[:linked][:users].size).to eq 2
    end
  end
end
