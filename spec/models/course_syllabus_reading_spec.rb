require 'spec_helper'

RSpec.describe Course, type: :model do
  describe 'syllabus reading requirement' do
    let(:course) { Course.create!(name: 'Test Course', account: Account.default) }
    let(:student) { User.create!(name: 'Test Student') }
    let(:enrollment) { course.enroll_student(student, enrollment_state: 'active') }

    describe '#syllabus_reading_required?' do
      it 'returns true by default (course has default syllabus template)' do
        expect(course.syllabus_reading_required?).to be true
      end

      it 'returns false when syllabus body is explicitly set to nil' do
        course.syllabus_body = nil
        expect(course.syllabus_reading_required?).to be false
      end

      it 'returns true when syllabus body is set' do
        course.syllabus_body = 'This is the syllabus content'
        expect(course.syllabus_reading_required?).to be true
      end
    end

    describe '#student_has_read_syllabus?' do
      context 'when syllabus reading is not required (no syllabus body)' do
        before do
          course.syllabus_body = nil # Remove the default syllabus
          enrollment # Create the enrollment
        end

        it 'returns true' do
          expect(course.student_has_read_syllabus?(student)).to be true
        end
      end

      context 'when syllabus reading is required (syllabus body exists)' do
        before do
          course.syllabus_body = 'This is the syllabus content'
          enrollment # Create the enrollment
        end

        it 'returns false when student has not read syllabus' do
          expect(course.student_has_read_syllabus?(student)).to be false
        end

        it 'returns true when student has read syllabus' do
          enrollment.mark_syllabus_as_read!
          expect(course.student_has_read_syllabus?(student)).to be true
        end
      end
    end

    describe '#mark_syllabus_read_for_student' do
      before do
        course.syllabus_body = 'This is the syllabus content'
        enrollment # Create the enrollment
      end

      it 'marks syllabus as read for the student' do
        expect(enrollment.syllabus_read?).to be false
        course.mark_syllabus_read_for_student(student)
        enrollment.reload
        expect(enrollment.syllabus_read?).to be true
        expect(enrollment.syllabus_read_at).to be_present
      end
    end

    describe 'syllabus update tracking' do
      before do
        course.update!(syllabus_body: 'Original syllabus content')
        # Set student as having read the syllabus AFTER the initial syllabus was set
        enrollment.update!(syllabus_read_at: Time.current)
      end

      it 'updates syllabus_updated_at when syllabus_body changes' do
        expect {
          course.update!(syllabus_body: 'Updated syllabus content')
        }.to change { course.syllabus_updated_at }
      end

      it 'student loses access after syllabus update and must re-read' do
        # Student has read the original syllabus
        expect(course.student_has_read_syllabus?(student)).to be true

        # Update the syllabus
        course.update!(syllabus_body: 'Updated syllabus content')

        # Student should lose access and need to re-read
        expect(course.student_has_read_syllabus?(student)).to be false
      end

      it 'handles case where syllabus_updated_at is nil' do
        course.update_column(:syllabus_updated_at, nil)
        expect(course.student_has_read_syllabus?(student)).to be true
      end

      it 'handles case where syllabus_read_at is nil' do
        enrollment.update_column(:syllabus_read_at, nil)
        expect(course.student_has_read_syllabus?(student)).to be false
      end
    end
  end
end

RSpec.describe Enrollment, type: :model do
  describe 'syllabus reading' do
    let(:course) { Course.create!(name: 'Test Course', account: Account.default) }
    let(:student) { User.create!(name: 'Test Student') }
    let(:enrollment) { course.enroll_student(student, enrollment_state: 'active') }

    describe '#syllabus_read?' do
      it 'returns false by default' do
        expect(enrollment.syllabus_read?).to be false
      end

      it 'returns true when marked as read' do
        enrollment.mark_syllabus_as_read!
        expect(enrollment.syllabus_read?).to be true
      end
    end

    describe '#mark_syllabus_as_read!' do
      it 'records timestamp when syllabus is read' do
        time_before = Time.current
        enrollment.mark_syllabus_as_read!
        expect(enrollment.syllabus_read_at).to be >= time_before
        expect(enrollment.syllabus_read_at).to be <= Time.current
      end
    end
  end
end
