/*
 * Copyright (C) 2025 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import $ from 'jquery'
import {showFlashAlert} from '@canvas/alerts/react/FlashAlert'
import {useScope as createI18nScope} from '@canvas/i18n'

const I18n = createI18nScope('syllabus_tab_locking')

class SyllabusTabLocking {
  constructor() {
    this.init()
  }

  init() {
    // Check if syllabus reading is required
    if (ENV.SYLLABUS_READING_REQUIRED) {
      console.log('SyllabusTabLocking: Syllabus reading is required, locking tabs')
      this.lockTabs()
    }

    // Bind click handlers to locked tabs
    this.bindLockedTabHandlers()

    // Listen for syllabus read events to unlock tabs
    $(document).on('syllabus:read', this.handleSyllabusRead.bind(this))
  }

  bindLockedTabHandlers() {
    // With the new implementation, tabs are visible but show restricted content
    // when syllabus reading is required. This method handles clicks on locked tabs.

    // Handle clicks on locked section tabs
    $(document).on('click', '#section-tabs li.locked a', (event) => {
      event.preventDefault()
      event.stopPropagation()

      this.showLockedMessage()
      return false
    })

    // Also handle any other locked navigation elements
    $(document).on('click', '.section-locked a, a.locked', (event) => {
      event.preventDefault()
      event.stopPropagation()

      this.showLockedMessage()
      return false
    })
  }

  showLockedMessage() {
    showFlashAlert({
      message: I18n.t('Please complete the syllabus before accessing this topic.'),
      type: 'warning',
      srOnly: false
    })
  }

  lockTabs() {
    console.log('SyllabusTabLocking: Locking course tabs')

    // Add locked class to all section tabs except syllabus
    $('#section-tabs li.section').each(function() {
      const $tab = $(this)
      const $link = $tab.find('a')

      // Don't lock the syllabus tab itself
      if (!$link.hasClass('syllabus')) {
        $tab.addClass('locked')
        $link.addClass('locked')

        // Add visual indication that tab is locked
        $tab.css({
          'opacity': '0.6',
          'pointer-events': 'none'
        })
      }
    })
  }

  handleSyllabusRead() {
    // When syllabus is read, refresh page to show all tabs
    this.unlockTabs()
  }

  unlockTabs() {
    console.log('SyllabusTabLocking: Unlocking tabs - refreshing page to show content')

    // Since tabs now show restricted content when locked (not hidden),
    // we need to refresh the page to show the actual course content
    // The server-side logic will now show normal content since syllabus is read

    // Show success message before refresh
    showFlashAlert({
      message: I18n.t('Syllabus completed! Refreshing to show course content...'),
      type: 'success',
      srOnly: false
    })

    // Small delay to let the user see the success message, then refresh
    setTimeout(() => {
      window.location.reload()
    }, 1500)
  }

  // Static method to initialize the tab locking system
  static init() {
    if (!window.syllabusTabLocking) {
      window.syllabusTabLocking = new SyllabusTabLocking()
    }
    return window.syllabusTabLocking
  }
}

export default SyllabusTabLocking
