/*
 * Copyright (C) 2025 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import $ from 'jquery'
import doFetchApi from '@canvas/do-fetch-api-effect'

class SyllabusReadTracker {
  constructor(courseId) {
    this.courseId = courseId
    this.hasScrolledToBottom = false
    this.isTracking = false
    this.debounceTimer = null
    this.debounceDelay = 2000 // 2 second delay before marking as read
    this.intersectionObserver = null

    this.init()
  }

  async init() {
    try {
      // Check if syllabus reading is required and if user has already read it
      const status = await this.checkSyllabusStatus()
      
      if (status.syllabus_required && !status.syllabus_read) {
        this.startTracking()
      }
    } catch (error) {
      console.error('Failed to initialize syllabus read tracker:', error)
    }
  }

  async checkSyllabusStatus() {
    const response = await fetch(`/api/v1/courses/${this.courseId}/syllabus_read_status`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      credentials: 'same-origin'
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    return await response.json()
  }

  startTracking() {
    this.isTracking = true
    console.log('SyllabusReadTracker: Starting to track syllabus reading')

    // Find the target element to observe
    const syllabusContent = document.getElementById('course_syllabus')

    console.log('SyllabusReadTracker: syllabusContent found:', !!syllabusContent)
   
    // Determine what element to observe for intersection
    let targetElement = null

    if (syllabusContent) {
      // If there's no syllabusContainer but there is syllabus content,
      // we need to create a marker element at the end of the syllabus content
      targetElement = this.createEndMarker(syllabusContent)
      console.log('SyllabusReadTracker: Created end marker as intersection target')
    }

    if (targetElement) {
      this.setupIntersectionObserver(targetElement)
    } else {
      console.warn('SyllabusReadTracker: No suitable target element found for intersection observation')
    }
  }

  createEndMarker(syllabusContent) {
    // Create a small invisible marker element at the end of the syllabus content
    const marker = document.createElement('div')
    marker.id = 'syllabus-end-marker'
    marker.style.height = '1px'
    marker.style.width = '100%'
    marker.style.visibility = 'hidden'
    marker.setAttribute('aria-hidden', 'true')

    // Insert the marker right after the syllabus content
    syllabusContent.parentNode.insertBefore(marker, syllabusContent.nextSibling)

    return marker
  }

  setupIntersectionObserver(targetElement) {
    console.log('SyllabusReadTracker: Setting up IntersectionObserver for element:', targetElement.id || targetElement.tagName)

    // Create intersection observer with a threshold that requires the element to be partially visible
    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && this.isTracking && !this.hasScrolledToBottom) {
          console.log('SyllabusReadTracker: Target element intersected, user has reached end of syllabus')
          this.handleIntersection()
        }
      })
    }, {
      // Trigger when at least 10% of the target element is visible
      threshold: 0.1,
      // Add some margin to trigger slightly before the element is fully in view
      rootMargin: '0px 0px -50px 0px'
    })

    this.intersectionObserver.observe(targetElement)
    console.log('SyllabusReadTracker: IntersectionObserver started observing target element')
  }

  handleIntersection() {
    console.log('SyllabusReadTracker: Intersection detected, marking syllabus as read')
    this.hasScrolledToBottom = true

    // Debounce the marking as read to ensure user actually spent time viewing the content
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
    }

    this.debounceTimer = setTimeout(() => {
      this.markSyllabusAsRead()
    }, this.debounceDelay)
  }

  async markSyllabusAsRead() {
    if (!this.isTracking) {
      return
    }

    console.log('SyllabusReadTracker: Attempting to mark syllabus as read')

    try {
      const {json, response} = await doFetchApi({
        path: `/api/v1/courses/${this.courseId}/mark_syllabus_read`,
        method: 'POST'
      })

      console.log(`SyllabusReadTracker: API response status: ${response.status}`)
      console.log('SyllabusReadTracker: API response:', json)

      if (json && json.success) {
        console.log('SyllabusReadTracker: Successfully marked syllabus as read')
        this.stopTracking()
        this.showSuccessMessage()

        // Trigger a custom event that other parts of the app can listen to
        $(document).trigger('syllabus:read', {courseId: this.courseId})
      } else {
        console.error('SyllabusReadTracker: Failed to mark syllabus as read - API returned success: false')
      }
    } catch (error) {
      console.error('SyllabusReadTracker: Error marking syllabus as read:', error)
    }
  }

  showSuccessMessage() {
    // showFlashAlert({
    //   message: 'Great! You have completed reading the syllabus. You now have access to all course content.',
    //   type: 'success'
    // })

    // Don't show a flash message that might cause page refresh
    // The SyllabusTabLocking will show its own success message
    console.log('SyllabusReadTracker: Syllabus marked as read, tabs should now be unlocked')
  }

  stopTracking() {
    this.isTracking = false

    // Disconnect intersection observer
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect()
      this.intersectionObserver = null
      console.log('SyllabusReadTracker: IntersectionObserver disconnected')
    }

    // Clean up any created marker elements
    const marker = document.getElementById('syllabus-end-marker')
    if (marker) {
      marker.remove()
      console.log('SyllabusReadTracker: End marker removed')
    }

    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
      this.debounceTimer = null
    }
  }

  destroy() {
    this.stopTracking()
  }
}

export default SyllabusReadTracker
