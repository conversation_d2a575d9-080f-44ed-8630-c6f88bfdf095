/*
 * Copyright (C) 2025 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import $ from 'jquery'
import SyllabusTabLocking from '../SyllabusTabLocking'

// Mock the flash alert function
jest.mock('@canvas/alerts/react/FlashAlert', () => ({
  showFlashAlert: jest.fn(),
}))

// Mock I18n
jest.mock('@canvas/i18n', () => ({
  useScope: () => ({
    t: jest.fn((key, defaultValue) => defaultValue || key),
  }),
}))

describe('SyllabusTabLocking', () => {
  let container
  let syllabusTabLocking

  beforeEach(() => {
    // Create a container for our test DOM elements
    container = document.createElement('div')
    container.innerHTML = `
      <nav>
        <ul id="section-tabs">
          <li class="section">
            <a href="/courses/123/assignments" class="assignments">
              Assignments
            </a>
          </li>
          <li class="section">
            <a href="/courses/123/discussion_topics" class="discussions">
              Discussions
            </a>
          </li>
          <li class="section">
            <a href="/courses/123/assignments/syllabus" class="syllabus">
              Syllabus
            </a>
          </li>
        </ul>
      </nav>
      <div id="main-content">
        <div class="syllabus-reading-required" style="display: none;">
          <p>Please read and complete the course syllabus before accessing this content.</p>
          <a href="/courses/123/assignments/syllabus" class="btn btn-primary">Read Syllabus</a>
        </div>
        <div class="course-content">
          <p>This is the normal course content.</p>
        </div>
      </div>
    `
    document.body.appendChild(container)

    // Initialize the tab locking system
    syllabusTabLocking = new SyllabusTabLocking()
  })

  afterEach(() => {
    // Clean up
    document.body.removeChild(container)
    $(document).off('syllabus:read')
    $(document).off('click')
    
    // Reset window.syllabusTabLocking
    if (window.syllabusTabLocking) {
      delete window.syllabusTabLocking
    }
  })

  describe('initialization', () => {
    it('creates a singleton instance', () => {
      const instance1 = SyllabusTabLocking.init()
      const instance2 = SyllabusTabLocking.init()
      expect(instance1).toBe(instance2)
      expect(window.syllabusTabLocking).toBe(instance1)
    })
  })

  describe('unlockTabs', () => {
    // Mock window.location.reload for testing
    const originalReload = window.location.reload
    beforeEach(() => {
      window.location.reload = jest.fn()
    })
    afterEach(() => {
      window.location.reload = originalReload
    })

    it('refreshes the page when syllabus is read', () => {
      // Mock setTimeout to avoid waiting
      jest.useFakeTimers()

      // Trigger syllabus read event
      $(document).trigger('syllabus:read', { courseId: '123' })

      // Fast-forward time to trigger the setTimeout callback
      jest.advanceTimersByTime(1500)

      // Verify page refresh was called
      expect(window.location.reload).toHaveBeenCalled()

      jest.useRealTimers()
    })

    it('shows success message before refreshing', () => {
      const { showFlashAlert } = require('@canvas/alerts/react/FlashAlert')

      // Trigger syllabus read event
      $(document).trigger('syllabus:read', { courseId: '123' })

      // Verify success message was shown
      expect(showFlashAlert).toHaveBeenCalledWith({
        message: 'Syllabus completed! Refreshing to show course content...',
        type: 'success',
        srOnly: false
      })
    })
  })

  describe('event handling', () => {
    it('initializes event handlers without errors', () => {
      // Test that the SyllabusTabLocking class can be instantiated
      // and its event handlers can be bound without throwing errors
      expect(() => {
        const instance = new SyllabusTabLocking()
        instance.bindLockedTabHandlers()
      }).not.toThrow()
    })

    it('handles syllabus:read event', () => {
      // Mock console.log to verify the event is handled
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()

      // Trigger syllabus read event
      $(document).trigger('syllabus:read', { courseId: '123' })

      // Verify the event was handled (unlockTabs method was called)
      expect(consoleSpy).toHaveBeenCalledWith('SyllabusTabLocking: Unlocking tabs - refreshing page to show content')

      consoleSpy.mockRestore()
    })
  })
})
