/*
 * Copyright (C) 2015 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import React from 'react'
import {useScope as createI18nScope} from '@canvas/i18n'
import {Modal} from '@instructure/ui-modal'
import {Button} from '@instructure/ui-buttons'
import {Heading} from '@instructure/ui-heading'
import {Text} from '@instructure/ui-text'
import {Flex} from '@instructure/ui-flex'

const I18n = createI18nScope('dashcards')

interface ConfirmPriorityCourseModalProps {
  courseId: string
  courseName: string
  onConfirm: () => void
  isOpen: boolean
  onClose: () => void
  isRemoving: boolean // Add flag to distinguish between add/remove
}

export const ConfirmPriorityCourseModal: React.FC<ConfirmPriorityCourseModalProps> = ({
  courseId,
  courseName,
  onConfirm,
  isOpen,
  onClose,
  isRemoving,
}) => {
  const handleConfirm = () => {
    onConfirm()
    onClose()
  }

  const modalTitle = isRemoving 
    ? I18n.t('Remove from Priority Courses?')
    : I18n.t('Add to Priority Courses?')

  const modalMessage = isRemoving
    ? I18n.t('Are you sure you want to remove "%{courseName}" from your priority courses?  ', {
        courseName,
      })
    : I18n.t('Are you sure you want to add "%{courseName}" to your priority courses? ', {
        courseName,
      })

  const confirmButtonText = isRemoving
    ? I18n.t('Remove from Priority')
    : I18n.t('Add to Priority')

  const confirmButtonColor = isRemoving ? 'danger' : 'primary'

  return (
    <Modal
      open={isOpen}
      onDismiss={onClose}
      size="small"
      label={modalTitle}
      shouldCloseOnDocumentClick={true}
    >
      <Modal.Header>
        <Heading level="h2">{modalTitle}</Heading>
      </Modal.Header>
      <Modal.Body>
        <Text>
          {modalMessage}
        </Text>
        {!isRemoving && (
          <>
            <br />
            <Text size="small" color="secondary">
              {I18n.t('You can have up to 5 priority courses at a time.')}
            </Text>
          </>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Flex justifyItems="end" gap="small">
          <Button onClick={onClose} color="secondary">
            {I18n.t('Cancel')}
          </Button>
          <Button onClick={handleConfirm} color={confirmButtonColor}>
            {confirmButtonText}
          </Button>
        </Flex>
      </Modal.Footer>
    </Modal>
  )
}

// Function to show the modal (similar to showConfirmUnfavorite)
let modalContainer: HTMLElement | null = null
let modalRoot: any = null

export const showConfirmPriority = (props: {
  courseId: string
  courseName: string
  onConfirm: () => void
  isRemoving: boolean
}) => {
  // Clean up any existing modal
  if (modalContainer && modalRoot) {
    modalRoot.unmount()
    document.body.removeChild(modalContainer)
  }

  // Create new modal container
  modalContainer = document.createElement('div')
  document.body.appendChild(modalContainer)

  // Import ReactDOM dynamically or use the global if available
  const ReactDOM = (window as any).ReactDOM || require('react-dom/client')
  
  // Handle both legacy and new ReactDOM APIs
  if (ReactDOM.createRoot) {
    modalRoot = ReactDOM.createRoot(modalContainer)
  } else {
    modalRoot = {
      render: (element: React.ReactElement) => ReactDOM.render(element, modalContainer),
      unmount: () => ReactDOM.unmountComponentAtNode(modalContainer)
    }
  }

  const closeModal = () => {
    if (modalRoot) {
      modalRoot.unmount()
    }
    if (modalContainer) {
      document.body.removeChild(modalContainer)
      modalContainer = null
      modalRoot = null
    }
  }

  const ModalWrapper = () => {
    const [isOpen, setIsOpen] = React.useState(true)

    const handleClose = () => {
      setIsOpen(false)
      setTimeout(closeModal, 150) // Small delay for close animation
    }

    const handleConfirm = () => {
      props.onConfirm()
      handleClose()
    }

    return (
      <ConfirmPriorityCourseModal
        courseId={props.courseId}
        courseName={props.courseName}
        onConfirm={handleConfirm}
        isOpen={isOpen}
        onClose={handleClose}
        isRemoving={props.isRemoving}
      />
    )
  }

  modalRoot.render(<ModalWrapper />)
}

export default ConfirmPriorityCourseModal