/*
 * Copyright (C) 2018 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import React, {useState} from 'react'
import {useScope as createI18nScope} from '@canvas/i18n'
import {Checkbox} from '@instructure/ui-checkbox'
import {View} from '@instructure/ui-view'
import {TextInput} from '@instructure/ui-text-input'
import {Text} from '@instructure/ui-text'
import {Flex} from '@instructure/ui-flex'

const I18n = createI18nScope('gradebook')

type AssignmentGroup = {
  id: string
  name: string
  group_weight: number
}

type Props = {
  assignmentGroups: AssignmentGroup[]
  applyAssignmentGroupWeights: boolean
  onApplyWeightsChange: (apply: boolean) => void
  onWeightsChange: (weights: {[groupId: string]: number}) => void
  gradebookIsEditable: boolean
}

const AssignmentGroupWeightsTabPanel: React.FC<Props> = ({
  assignmentGroups,
  applyAssignmentGroupWeights,
  onApplyWeightsChange,
  onWeightsChange,
  gradebookIsEditable,
}) => {
  const [groupWeights, setGroupWeights] = useState<{[groupId: string]: number}>(() => {
    const weights: {[groupId: string]: number} = {}
    assignmentGroups.forEach(group => {
      weights[group.id] = group.group_weight || 0
    })
    return weights
  })

  const handleWeightChange = (groupId: string, value: string) => {
    const numValue = parseFloat(value) || 0
    const newWeights = {...groupWeights, [groupId]: numValue}
    setGroupWeights(newWeights)
    onWeightsChange(newWeights)
  }

  const totalWeight = Object.values(groupWeights).reduce((sum, weight) => sum + weight, 0)

  return (
    <View as="div" padding="large">
      <View as="div" margin="0 0 large 0">
        <Checkbox
          label={I18n.t('Weight final grade based on assignment groups')}
          checked={applyAssignmentGroupWeights}
          onChange={(e) => onApplyWeightsChange(e.target.checked)}
          disabled={!gradebookIsEditable}
        />
      </View>
      
      {applyAssignmentGroupWeights && (
        <View as="div" margin="0 0 0 medium">
          {assignmentGroups.map((group, index) => (
            <View key={group.id} as="div" margin="0 0 medium 0">
              <Flex alignItems="center" gap="medium">
                <Flex.Item shouldGrow>
                  <Text size="medium" weight="normal">
                    {group.name} (%)
                  </Text>
                </Flex.Item>
                <Flex.Item>
                  <TextInput
                    renderLabel=""
                    value={groupWeights[group.id]?.toString() || '0'}
                    onChange={(e) => handleWeightChange(group.id, e.target.value)}
                    disabled={!gradebookIsEditable}
                    width="70px"
                    textAlign="center"
                    interaction={gradebookIsEditable ? 'enabled' : 'disabled'}
                  />
                </Flex.Item>
              </Flex>
            </View>
          ))}
          
          <View as="div" borderWidth="0 0 small 0" borderColor="primary" margin="medium 0" />
          
          <Flex alignItems="center" gap="medium" margin="medium 0 0 0">
            <Flex.Item shouldGrow>
              <Text size="medium" weight="bold">
                Total
              </Text>
            </Flex.Item>
            <Flex.Item>
              <View as="div" width="70px" textAlign="center">
                <Text size="medium" weight="bold">
                  {totalWeight}%
                </Text>
              </View>
            </Flex.Item>
          </Flex>
        </View>
      )}
    </View>
  )
}

export default AssignmentGroupWeightsTabPanel