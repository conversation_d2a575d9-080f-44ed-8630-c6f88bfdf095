/*
 * Copyright (C) 2011 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import ready from '@instructure/ready'

// Tab functionality
function initializeTabs() {
  const tabButtons = document.querySelectorAll('.tab-button');
  const tabContents = document.querySelectorAll('.tab-content');

  tabButtons.forEach(button => {
    button.addEventListener('click', function() {
      const targetTab = this.getAttribute('data-tab');
      
      // Remove active class from all buttons and contents
      tabButtons.forEach(btn => {
        btn.classList.remove('active');
        btn.setAttribute('aria-selected', 'false');
      });
      tabContents.forEach(content => {
        content.classList.remove('active');
      });
      
      // Add active class to clicked button and corresponding content
      this.classList.add('active');
      this.setAttribute('aria-selected', 'true');
      document.getElementById(targetTab).classList.add('active');
    });
  });
}

function initializeMonthlyReports() {
  initializeReportGeneration();
  initializeReportFilters();
  initializeReportActions();
}

function initializeReportGeneration() {
  const generateForm = document.getElementById('generate-report-form');
  const generateBtn = document.getElementById('generate-report-btn');

  if (generateForm && generateBtn) {
    generateForm.addEventListener('submit', function(e) {
      // Validate that at least month and year are selected
      const monthSelect = generateForm.querySelector('select[name="month"]');
      const yearSelect = generateForm.querySelector('select[name="year"]');
      
      if (!monthSelect.value || !yearSelect.value) {
        e.preventDefault();
        return;
      }
      
      // Show loading state
      generateBtn.disabled = true;
      generateBtn.innerHTML = '⏳ Generating Report...';
      
      const month = monthSelect.options[monthSelect.selectedIndex].text;
      const year = yearSelect.value;
      const format = generateForm.querySelector('select[name="report_format"]').value.toUpperCase();
      
      
      // Re-enable after a delay in case of errors
      setTimeout(() => {
        generateBtn.disabled = false;
        generateBtn.innerHTML = 'Generate Report';
      }, 15000); // Longer timeout for report generation
    });

    // Add change listeners to update UI when filters change
    const filterSelects = generateForm.querySelectorAll('select[name^="filter_"], select[name="month"], select[name="year"]');
    filterSelects.forEach(select => {
      select.addEventListener('change', function() {
        updateReportPreview();
      });
    });
  }
}

// Update report preview information
function updateReportPreview() {
  const form = document.getElementById('generate-report-form');
  if (!form) return;
  
  const month = form.querySelector('select[name="month"]');
  const year = form.querySelector('select[name="year"]');
  const role = form.querySelector('select[name="filter_role"]');
  const department = form.querySelector('select[name="filter_department"]');
  const activity = form.querySelector('select[name="filter_activity"]');
  
  if (month && year) {
    const monthName = month.options[month.selectedIndex].text;
    const yearValue = year.value;
    
    // Update any preview elements if they exist
    const previewElements = document.querySelectorAll('.report-preview');
    previewElements.forEach(element => {
      element.textContent = `${monthName} ${yearValue}`;
    });
  }
}

// Initialize report filtering (existing functionality)
function initializeReportFilters() {
  const searchInput = document.getElementById('report-search');
  const yearFilter = document.getElementById('report-year-filter');
  const typeFilter = document.getElementById('report-type-filter');

  if (searchInput) {
    searchInput.addEventListener('input', filterReports);
  }

  if (yearFilter) {
    yearFilter.addEventListener('change', filterReports);
  }

  if (typeFilter) {
    typeFilter.addEventListener('change', filterReports);
  }
}

// Filter reports based on search criteria (existing functionality)
function filterReports() {
  const searchTerm = document.getElementById('report-search')?.value.toLowerCase() || '';
  const yearFilter = document.getElementById('report-year-filter')?.value || '';
  const typeFilter = document.getElementById('report-type-filter')?.value || '';
  
  const reportRows = document.querySelectorAll('.report-row');
  let visibleCount = 0;
  
  reportRows.forEach(row => {
    const monthYear = row.querySelector('td:first-child strong')?.textContent.toLowerCase() || '';
    const rowYear = row.getAttribute('data-year') || '';
    const rowType = row.getAttribute('data-type') || '';
    const generatedBy = row.querySelector('.user-info')?.textContent.toLowerCase() || '';
    const filters = row.querySelector('.filters-info')?.textContent.toLowerCase() || '';
    
    const matchesSearch = searchTerm === '' || 
      monthYear.includes(searchTerm) || 
      generatedBy.includes(searchTerm) || 
      filters.includes(searchTerm);
    
    const matchesYear = yearFilter === '' || rowYear === yearFilter;
    const matchesType = typeFilter === '' || rowType === typeFilter;
    
    const shouldShow = matchesSearch && matchesYear && matchesType;
    row.style.display = shouldShow ? '' : 'none';
    
    if (shouldShow) visibleCount++;
  });
  
  // Update "no reports" message if needed
  const noReportsRow = document.querySelector('.no-reports');
  if (noReportsRow) {
    const hasReports = document.querySelectorAll('.report-row').length > 0;
    if (hasReports && visibleCount === 0) {
      noReportsRow.style.display = '';
      noReportsRow.textContent = 'No reports match the current filters.';
    } else if (!hasReports) {
      noReportsRow.style.display = '';
      noReportsRow.textContent = 'No saved reports found. Generate a report using the form above.';
    } else {
      noReportsRow.style.display = 'none';
    }
  }
}

// Initialize report action buttons (existing functionality)
function initializeReportActions() {
  const reportsTable = document.querySelector('.reports-table tbody');
  if (reportsTable) {
    bindReportActions(reportsTable);
  }
}

// Bind action buttons for reports (existing functionality)
function bindReportActions(container) {
  // Download buttons (anchor tags) - these should work automatically
  
  // Delete buttons
  container.querySelectorAll('.delete-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      const filename = this.getAttribute('data-filename');
      const reportName = this.getAttribute('data-report-name') || 'this report';
      deleteReport(filename, reportName);
    });
  });
}


function deleteReport(filename, reportName) {
  const accountId = getAccountIdFromPage();

  if (!accountId) {
    return;
  }
  
  // Try to get CSRF token from the generate report form
  let csrfToken = null;
  
  // Look for the generate report form
  const generateForm = document.getElementById('generate-report-form');
  if (generateForm) {
    const tokenInput = generateForm.querySelector('input[name="authenticity_token"]');
    if (tokenInput) {
      csrfToken = tokenInput.value;
    }
  }
  
  // Fallback to other forms
  if (!csrfToken) {
    const allForms = document.querySelectorAll('form');
    for (const form of allForms) {
      const tokenInput = form.querySelector('input[name="authenticity_token"]');
      if (tokenInput && tokenInput.value) {
        csrfToken = tokenInput.value;
        break;
      }
    }
  }
  
  // Try meta tag as last resort
  if (!csrfToken) {
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
      csrfToken = metaToken.getAttribute('content');
    }
  }
  
  if (!csrfToken) {
    return;
  }
  
  const url = `/accounts/${accountId}/delete_monthly_report?filename=${encodeURIComponent(filename)}`;
  
  if (confirm(`Are you sure you want to delete "${reportName}"? This action cannot be undone.`)) {
    const btn = document.querySelector(`[data-filename="${filename}"].delete-btn`);
    if (!btn) {
      return;
    }
    
    const row = btn.closest('tr');
    const originalContent = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = '⏳';
    
    fetch(url, {
      method: 'DELETE',
      headers: {
        'X-CSRF-Token': csrfToken,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })
    .then(response => {
      
      // Get the raw response text first
      return response.text().then(text => {
  
        
        // Check if it's HTML (common when there's a redirect or error)
        if (text.trim().startsWith('<') || text.includes('<!DOCTYPE')) {
          throw new Error('Server returned HTML page instead of JSON. Check server logs for errors.');
        }
        
        try {
          const data = JSON.parse(text);
          return { data, response };
        } catch (e) {
          throw new Error(`Invalid JSON response: ${text.substring(0, 200)}...`);
        }
      });
    })
    .then(result => {
      const { data, response } = result;
      
      if (data.success) {
        row.remove();
        updateReportStats();
      } else {
        btn.disabled = false;
        btn.innerHTML = originalContent;
      }
    })
    .catch(error => {
      btn.disabled = false;
      btn.innerHTML = originalContent;
    });
  }
}


// Helper function to get account ID from page
function getAccountIdFromPage() {
  
  // Method 1: Try to get from URL
  const urlMatch = window.location.pathname.match(/\/accounts\/(\d+)/);
  if (urlMatch) {
    return urlMatch[1];
  }
  
  // Method 2: Try to get from a data attribute on body or html
  const bodyAccountId = document.body.getAttribute('data-account-id');
  if (bodyAccountId) {
    return bodyAccountId;
  }
  
  // Method 3: Try to get from any element with data-account-id
  const accountElement = document.querySelector('[data-account-id]');
  if (accountElement) {
    const accountId = accountElement.getAttribute('data-account-id');
    return accountId;
  }
  
  // Method 4: Look for it in any form action URLs
  const forms = document.querySelectorAll('form[action*="/accounts/"]');
  for (const form of forms) {
    const actionMatch = form.action.match(/\/accounts\/(\d+)/);
    if (actionMatch) {
      return actionMatch[1];
    }
  }
  
  // Method 5: Look for it in any link hrefs
  const links = document.querySelectorAll('a[href*="/accounts/"]');
  for (const link of links) {
    const hrefMatch = link.href.match(/\/accounts\/(\d+)/);
    if (hrefMatch) {
      return hrefMatch[1];
    }
  }
  
  return null;
}

// Update report statistics after deletion
function updateReportStats() {
  const visibleRows = document.querySelectorAll('.report-row:not([style*="display: none"])');
  const totalReports = visibleRows.length;
  const currentYear = new Date().getFullYear();
  const reportsThisYear = Array.from(visibleRows).filter(row => 
    parseInt(row.getAttribute('data-year')) === currentYear
  ).length;
  const csvReports = Array.from(visibleRows).filter(row => 
    row.getAttribute('data-type') === 'csv'
  ).length;
  const pdfReports = Array.from(visibleRows).filter(row => 
    row.getAttribute('data-type') === 'pdf'
  ).length;
  
  // Update stat cards if they exist
  const statCards = document.querySelectorAll('.stat-card .stat-value');
  if (statCards.length >= 4) {
    statCards[0].textContent = totalReports;
    statCards[1].textContent = reportsThisYear;
    statCards[2].textContent = pdfReports;
    statCards[3].textContent = `${(totalReports * 0.5).toFixed(1)} MB`; // Approximate storage
  }
}

// Initialize all functionality when DOM is ready
ready(() => {
  initializeTabs();
  initializeMonthlyReports();
});

// Export functions for potential external use
export { 
  initializeTabs, 
  initializeMonthlyReports
};