/*
 * Copyright (C) 2025 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import React, {useState} from 'react'
import {useScope as useI18nScope} from '@canvas/i18n'
import {Button} from '@instructure/ui-buttons'
import {FormFieldGroup} from '@instructure/ui-form-field'
import {TextInput} from '@instructure/ui-text-input'
import {RadioInputGroup, RadioInput} from '@instructure/ui-radio-input'
import {View} from '@instructure/ui-view'
import {ScreenReaderContent} from '@instructure/ui-a11y-content'
import {DateTimeInput} from '@instructure/ui-date-time-input'
import {SimpleSelect} from '@instructure/ui-simple-select'
import AutoCompleteSelect from '../../../shared/auto-complete-select/react/AutoCompleteSelect'
import PropTypes from 'prop-types'
import moment from 'moment'
import {IconCoursesSolid, IconUserSolid, IconAssignmentLine} from '@instructure/ui-icons'

const AssignmentActivityForm = ({accountId, onSubmit}) => {
  const I18n = useI18nScope('assignment_activity_form')
  const [type, setType] = useState('courses')
  const [courseId, setCourseId] = useState('')
  const [assignmentId, setAssignmentId] = useState('')
  const [userId, setUserId] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [eventType, setEventType] = useState('all')
  
  // Track the display values separately from the IDs
  const [assignmentDisplayValue, setAssignmentDisplayValue] = useState('')
  const [userDisplayValue, setUserDisplayValue] = useState('')

  // Reset all form fields when search type changes
  const resetFormFields = () => {
    setCourseId('')
    setAssignmentId('')
    setUserId('')
    setAssignmentDisplayValue('')
    setUserDisplayValue('')
    setStartDate('')
    setEndDate('')
    setEventType('all')
  }

  // Handle search type change
  const handleTypeChange = (e, value) => {
    setType(value)
    resetFormFields()
  }

  const handleSubmit = e => {
    e.preventDefault()
    
    const params = {type}
    
    // Helper function to ensure we're sending string values
    const getValue = (value) => {
      if (!value) return ''
      return value.toString()
    }
    
    if (type === 'courses') {
      params.course_id = getValue(courseId)
    } else if (type === 'assignments') {
      params.assignment_id = getValue(assignmentId)
    } else if (type === 'users') {
      params.user_id = getValue(userId)
    }
    
    if (startDate) {
      params.start_time = moment(startDate).toISOString()
    }
    
    if (endDate) {
      params.end_time = moment(endDate).toISOString()
    }
    
    // Only add event_type if it's not 'all' and is a valid value
    if (eventType && eventType !== 'all') {
      // Ensure we're just sending the string value, not an object
      params.event_type = typeof eventType === 'object' ? eventType.value : eventType
    } else {
      params.event_type = 'all'
    }
    
    onSubmit(params)
  }

  return (
    <View as="div" padding="medium">
      <form onSubmit={handleSubmit} data-testid="assignment-activity-form">
        <FormFieldGroup
          description={""}
          layout="stacked"
        >
          <RadioInputGroup
            name="search_type"
            description={I18n.t('Search By')}
            value={type}
            onChange={handleTypeChange}
          >
            <RadioInput id="course_search" value="courses" label={I18n.t('By Course')} />
            <RadioInput id="assignment_search" value="assignments" label={I18n.t('By Assignment')} />
            <RadioInput id="user_search" value="users" label={I18n.t('By User')} />
          </RadioInputGroup>

          {type === 'courses' && (
            <TextInput
              renderLabel={I18n.t('Course ID')}
              isRequired
              placeholder={I18n.t('Example: 789')}
              value={courseId}
              onChange={e => setCourseId(e.target.value)}
            />
          )}

          {type === 'assignments' && (
            <View as="div" margin="0 0 medium 0">
              <View as="div" margin="0 0 medium 0">
                <TextInput
                  renderLabel={I18n.t('Course ID')}
                  isRequired
                  placeholder={I18n.t('Example: 789')}
                  value={courseId}
                  onChange={e => setCourseId(e.target.value)}
                />
              </View>

              <View as="div">
                <AutoCompleteSelect
                  renderLabel={I18n.t('Assignment')}
                  placeholder={I18n.t('Search by name or ID')}
                  assistiveText={I18n.t('Type to search')}
                  url={courseId ? `/api/v1/courses/${courseId}/assignments` : null}
                  renderOptionLabel={option => option.name ? option.name : option.id}
                  renderBeforeInput={<IconAssignmentLine inline={false} />}
                  messages={!courseId ? [{
                    text: I18n.t('Please select a course first'),
                    type: 'hint'
                  }] : []}
                  fetchParams={{
                    include: ['submission'],
                    per_page: '10'
                  }}
                  value={assignmentDisplayValue}
                  onInputChange={value => {
                    // Update the display value
                    const inputValue = typeof value === 'string' ? value : value?.target?.value || ''
                    setAssignmentDisplayValue(inputValue)
                    
                    // If the input is cleared, also clear the ID
                    if (!inputValue) {
                      setAssignmentId('')
                    } else {
                      // If it's a string input, update the assignment ID if it's a number
                      const id = inputValue.trim()
                      if (/^\d+$/.test(id)) {
                        setAssignmentId(id)
                      }
                    }
                  }}
                  onRequestSelectOption={(_, {id, name}) => {
                    setAssignmentId(id.toString())
                    setAssignmentDisplayValue(name)
                  }}
                  overrideSelectOptionProps={{
                    renderBeforeLabel: <IconAssignmentLine inline={false} />
                  }}
                  isDisabled={!courseId}
                />
              </View>
            </View>
          )}

          {type === 'users' && (
            <View as="div" margin="0 0 medium 0">
              <AutoCompleteSelect
                renderLabel={I18n.t('User')}
                placeholder={I18n.t('Search by name or ID')}
                assistiveText={I18n.t('Type to search')}
                url={`/api/v1/accounts/${accountId}/users`}
                renderOptionLabel={option => option.name ? option.name : option.id}
                renderBeforeInput={<IconUserSolid inline={false} />}
                fetchParams={{
                  include: ['email'],
                  per_page: '10',
                  enrollment_type: ['teacher', 'student', 'ta', 'observer', 'designer']
                }}
                value={userDisplayValue}
                onInputChange={value => {
                  // Update the display value
                  const inputValue = typeof value === 'string' ? value : value?.target?.value || ''
                  setUserDisplayValue(inputValue)
                  
                  // If the input is cleared, also clear the ID
                  if (!inputValue) {
                    setUserId('')
                  } else {
                    // If it's a string input, update the user ID if it's a number
                    const id = inputValue.trim()
                    if (/^\d+$/.test(id)) {
                      setUserId(id)
                    }
                  }
                }}
                onRequestSelectOption={(_, {id, name}) => {
                  setUserId(id.toString())
                  setUserDisplayValue(name)
                }}
                overrideSelectOptionProps={{
                  renderBeforeLabel: <IconUserSolid inline={false} />
                }}
              />
            </View>
          )}

          <View as="p" margin="small 0 medium 0">
            {I18n.t('filter_guidance', 'Please specify an Event Type or a date range to refine your search.')}
          </View>

          <SimpleSelect
            renderLabel="Event Type"
            value={eventType}
            onChange={(e, value) => {
              // Extract just the string value if we got an object
              const actualValue = typeof value === 'object' && value !== null ? value.value : value
              setEventType(actualValue)
            }}
          >
            <SimpleSelect.Option id="all" value="all">
              All Events
            </SimpleSelect.Option>
            <SimpleSelect.Option id="notification_sent" value="notification_sent">
              Notification Sent
            </SimpleSelect.Option>
            <SimpleSelect.Option id="due_date_changed" value="due_date_changed">
              Due Date Changed
            </SimpleSelect.Option>
          </SimpleSelect>

          <DateTimeInput
            description={''}
            dateRenderLabel={I18n.t('Start Date')}
            timeRenderLabel={I18n.t('Start Time')}
            prevMonthLabel={I18n.t('Previous month')}
            nextMonthLabel={I18n.t('Next month')}
            onChange={(e, value) => setStartDate(value)}
            layout="columns"
            value={startDate}
            invalidDateTimeMessage={I18n.t('invalid_datetime_format', 'Please enter a valid date and time.')}
          />

          <DateTimeInput
            description={''}
            dateRenderLabel={I18n.t('End Date')}
            timeRenderLabel={I18n.t('End Time')}
            layout="columns"
            prevMonthLabel={I18n.t('Previous month')}
            nextMonthLabel={I18n.t('Next month')}
            onChange={(e, value) => setEndDate(value)}
            value={endDate}
            invalidDateTimeMessage={I18n.t('invalid_datetime_format', 'Please enter a valid date and time.')}
          />

          <Button type="submit" color="primary">
            {I18n.t('Search')}
          </Button>
        </FormFieldGroup>
      </form>
    </View>
  )
}

AssignmentActivityForm.propTypes = {
  accountId: PropTypes.string.isRequired,
  onSubmit: PropTypes.func.isRequired,
}

export default AssignmentActivityForm
