/*
 * Copyright (C) 2025 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import Backbone from '@canvas/backbone'
import PaginatedCollection from '@canvas/pagination/backbone/collections/PaginatedCollection'
import {extend} from '@canvas/backbone/utils'

extend(AssignmentLoggingCollection, PaginatedCollection)

export default function AssignmentLoggingCollection() {
  this.setParams = this.setParams.bind(this)
  this.url = this.url.bind(this)
  PaginatedCollection.apply(this, arguments)
}

// Custom model that has access to the parent collection's linked data
const LoggingModel = Backbone.Model.extend({
  initialize(attributes, options) {
    // Make linked data available to this model
    if (options && options.collection && options.collection.linked) {
      this.linked = options.collection.linked;
    }
  },
  
  // Helper method to get linked data
  getLinked(type, id) {
    if (!this.linked || !this.linked[type]) return null;
    return this.linked[type].get(id);
  }
});

Object.assign(AssignmentLoggingCollection.prototype, {
  // Use our custom model
  model: LoggingModel,
  
  // Parse the API response to handle linked data
  parse(response) {
    // Store linked data in a format that's easy to access
    if (response.linked) {
      this.linked = {};
      
      // Convert each linked type to a Backbone.Collection
      Object.entries(response.linked).forEach(([type, items]) => {
        if (Array.isArray(items)) {
          this.linked[type] = new Backbone.Collection(items, {
            model: Backbone.Model.extend({ idAttribute: 'id' })
          });
        }
      });
    }
    
    // Return the events array for the main collection
    return response.events || [];
  },
  
  url() {
    const { params } = this.options;
    const { type, course_id, assignment_id, user_id, account_id, role, start_time, end_time, event_type } = params;
    let basePath = '/api/v1/audit/assignment';

    if (type === 'assignments') {
      basePath += `/assignments/${assignment_id}`;
    } else if (type === 'courses') {
      basePath += `/courses/${course_id}`;
    } else if (type === 'users') {
      basePath += `/users/${user_id}`;
    } else if (type === 'accounts') {
      basePath += `/accounts/${account_id}`;
    }

    return basePath;
  },

  setParams(params) {
    this.options = {params};
    this.trigger('setParams', this);
    return this.fetch({reset: true});
  }
})
