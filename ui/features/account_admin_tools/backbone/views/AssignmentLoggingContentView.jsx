/*
 * Copyright (C) 2025 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import React from 'react'
import {createRoot} from 'react-dom/client'
import Backbone from '@canvas/backbone'
import $ from 'jquery'
import PaginatedCollectionView from '@canvas/pagination/backbone/views/PaginatedCollectionView'
import ValidatedMixin from '@canvas/forms/backbone/views/ValidatedMixin'
import AssignmentLoggingItemView from './AssignmentLoggingItemView'
import AssignmentLoggingCollection from '../collections/AssignmentLoggingCollection'
import template from '../../jst/assignmentLoggingContent.handlebars'
import assignmentLoggingResultsTemplate from '../../jst/assignmentLoggingResults.handlebars'
import {extend} from '@canvas/backbone/utils'
import AssignmentActivityForm from '../../react/AssignmentActivityForm'

extend(AssignmentLoggingContentView, Backbone.View)

export default function AssignmentLoggingContentView(options) {
  this.fetch = this.fetch.bind(this)
  this.onFail = this.onFail.bind(this)
  this.options = options
  this.collection = new AssignmentLoggingCollection()
  Backbone.View.apply(this, arguments)
  this.resultsView = new PaginatedCollectionView({
    template: assignmentLoggingResultsTemplate,
    itemView: AssignmentLoggingItemView,
    collection: this.collection,
  })
}

AssignmentLoggingContentView.mixin(ValidatedMixin)

AssignmentLoggingContentView.child('resultsView', '#assignmentLoggingSearchResults')

Object.assign(AssignmentLoggingContentView.prototype, {
  template,

  afterRender() {
    const mountPoint = document.getElementById('assignment_activity_form_mount_point')
    const root = createRoot(mountPoint)

    root.render(
      <AssignmentActivityForm
        accountId={ENV.ACCOUNT_ID}
        onSubmit={data => {
          this.updateCollection(data)
        }}
      />,
    )
  },

  updateCollection(json) {
    return this.collection.setParams(json)
  },

  attach() {
    return this.collection.on('setParams', this.fetch)
  },

  fetch() {
    return this.collection.fetch({error: this.onFail})
  },

  onFail(collection, xhr) {
    // Received a 404, empty the collection and don't let the paginated
    // view try to fetch more.

    this.collection.reset()
    this.resultsView.detachScroll()
    this.resultsView.$el.find('.paginatedLoadingIndicator').fadeOut()

    const status = xhr != null ? xhr.status : undefined;

    // Clear previous general errors
    this.$el.find('#assignment_api_error_message').remove();

    if (status === 400) {
      this.collection.reset();
      this.resultsView.detachScroll();
      this.resultsView.$el.find('.paginatedLoadingIndicator').fadeOut();
      try {
        const response = JSON.parse(xhr.responseText);
        if (response && response.error) {
          // Display the error message. Adjust selector as needed.
          this.$el.find('#assignment_activity_form_mount_point').before(
            $('<div>').attr('id', 'assignment_api_error_message').addClass('alert alert-error').text(response.error)
          );
        }
      } catch (e) {
        // Handle JSON parse error or unexpected response structure
        this.$el.find('#assignment_activity_form_mount_point').before(
          $('<div>').attr('id', 'assignment_api_error_message').addClass('alert alert-error').text('An unexpected error occurred.')
        );
      }
    } else if (status === 404) {
      const {type} = this.collection.options.params
      const errors = {}

      if (type === 'courses') {
        errors.course_id = [
          {
            type: 'required',
            message: 'A course with that ID could not be found for this account.',
          },
        ]
      }

      if (type === 'assignments') {
        errors.assignment_id = [
          {
            type: 'required',
            message: 'An assignment with that ID could not be found for this account.',
          },
        ]
      }

      if (type === 'users') {
        errors.user_id = [
          {
            type: 'required',
            message: 'A user with that ID could not be found for this account.',
          },
        ]
      }

      if (!$.isEmptyObject(errors)) return this.showErrors(errors)
    }
  },
})
