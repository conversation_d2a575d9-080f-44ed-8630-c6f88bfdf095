/*
 * Copyright (C) 2025 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import Backbone from '@canvas/backbone'
import {useScope as useI18nScope} from '@canvas/i18n'
import template from '../../jst/assignmentLoggingItem.handlebars'
import {extend} from '@canvas/backbone/utils'
import '@canvas/jquery/jquery.ajaxJSON'
import {datetimeString} from '@canvas/datetime/date-functions'

const I18n = useI18nScope('assignment_logging_item_view')

extend(AssignmentLoggingItemView, Backbone.View)

export default function AssignmentLoggingItemView() {
  // this.events = this.events?.bind(this)
  // this.toJSON = this.toJSON?.bind(this)
  // this.formatEventData = this.formatEventData?.bind(this)
  Backbone.View.apply(this, arguments)
}

Object.assign(AssignmentLoggingItemView.prototype, {
  tagName: 'tr',
  className: 'assignmentAuditLogItem',
  template,

  events: {
    'click .assignmentAuditLogItem__moreInfo': 'toggleInfo',
  },

  formatEventData(eventData) {
    try {
      // Handle empty or null event_data
      if (!eventData) return { formatted: '' }
      
      // Handle both string and object formats for event_data
      let data = eventData;
      if (typeof eventData === 'string') {
        try {
          data = JSON.parse(eventData);
        } catch (parseError) {
          // If it's not valid JSON but is a string, return empty
          console.warn('Invalid JSON in event_data:', parseError);
          return { formatted: '' };
        }
      }
      
      const eventType = this.model.get('event_type')
      
      if (eventType === 'notification_sent' || eventType === 'email_sent') {
        if (data && data.notification_type) {
          return {
            notification_type: data.notification_type,
            formatted: I18n.t('Notification type: %{type}', {
              type: data.notification_type === 'late' ? I18n.t('Late') : I18n.t('Upcoming'),
            }),
          }
        }
        return { formatted: I18n.t('Notification sent') }
      } else if (eventType === 'due_date_changed' || eventType === 'due_date_updated') {
        // Handle missing date data
        if (!data || (!data.old_due_date && !data.new_due_date)) {
          return { formatted: I18n.t('Due date updated') }
        }
        
        try {
          const oldDate = data.old_due_date ? new Date(data.old_due_date) : null
          const newDate = data.new_due_date ? new Date(data.new_due_date) : null
          
          return {
            old_due_date: data.old_due_date,
            new_due_date: data.new_due_date,
            formatted: I18n.t('Due date changed from %{old} to %{new}', {
              old: oldDate ? oldDate.toLocaleString() : I18n.t('(none)'),
              new: newDate ? newDate.toLocaleString() : I18n.t('(none)'),
            }),
          }
        } catch (dateError) {
          console.error('Error parsing dates:', dateError)
          return { formatted: I18n.t('Due date updated (error parsing dates)') }
        }
      }
      
      // For any other event type with data, show a generic message
      if (data && typeof data === 'object' && Object.keys(data).length > 0) {
        return { formatted: JSON.stringify(data) }
      }
      
      return { formatted: '' }
    } catch (e) {
      console.error('Error formatting event data:', e)
      return { formatted: '' }
    }
  },

  toggleInfo(event) {
    event.preventDefault()
    const target = event.currentTarget
    const details = this.el.querySelector('.assignmentAuditLogItem__details')
    const isVisible = details.style.display !== 'none' && details.offsetParent !== null
    details.style.display = isVisible ? 'none' : 'block'
    target.textContent = isVisible ? I18n.t('Show Details') : I18n.t('Hide Details')
  },

  toJSON() {
    const modelData = this.model?.toJSON() || {};
    const json = {
      ...modelData,
      created_at: datetimeString(modelData.created_at),
    };

    // Add event data formatting
    const eventData = this.formatEventData(modelData.event_data || {});
    json.formattedEventData = eventData.formatted;
    
    // Set default values
    json.assignment_name = `Assignment ${modelData.assignment_id || 'Unknown'}`;
    json.course_name = `Course ${modelData.course_id || 'Unknown'}`;
    json.user_name = `User ${modelData.user_id || 'Unknown'}`;

    // Try to get linked data if available
    if (this.model && this.model.getLinked) {
      // Get assignment data
      const assignment = this.model.getLinked('assignments', modelData.assignment_id);
      if (assignment) {
        json.assignment_name = assignment.get('name');
      }
      
      // Get course data
      const course = this.model.getLinked('courses', modelData.course_id);
      if (course) {
        json.course_name = course.get('name');
      }
      
      // Get user data
      const user = this.model.getLinked('users', modelData.user_id);
      if (user) {
        json.user_name = user.get('name');
      }
    }

    // Add event type specific text
    if (json.event_type === 'notification_sent' || json.event_type === 'email_sent') {
      json.event_type_text = I18n.t('Assignment Notification Sent');
    } else if (json.event_type === 'due_date_changed' || json.event_type === 'due_date_updated') {
      json.event_type_text = I18n.t('Assignment Due Date Changed');
    } else if (json.event_type) {
      // Fallback for any other event type
      json.event_type_text = json.event_type
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase());
    }

    return json
  },
})
