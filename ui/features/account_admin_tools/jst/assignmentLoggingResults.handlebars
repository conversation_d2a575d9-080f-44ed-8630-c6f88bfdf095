{{#if collection.length}}
<div class="assignmentLoggingResults">
  <table class="ic-Table ic-Table--striped">
    <thead>
      <tr>
        <th>{{#t "date"}}Date{{/t}}</th>
        <th>{{#t "event_type"}}Event Type{{/t}}</th>
        <th>{{#t "assignment"}}Assignment{{/t}}</th>
        <th>{{#t "course"}}Course{{/t}}</th>
        <th>{{#t "user"}}User{{/t}}</th>
        <th>{{#t "details"}}Details{{/t}}</th>
      </tr>
    </thead>
    <tbody class="collectionViewItems"></tbody>
  </table>
  <div class="paginatedLoadingIndicator" style="display: none;">
    <img src="/images/ajax-loader-medium-444.gif" alt="{{#t "loading"}}Loading{{/t}}">
  </div>
</div>
{{else}}
<div class="alert alert-info">
  {{#t "no_items_found"}}No items found{{/t}}
</div>
{{/if}}
