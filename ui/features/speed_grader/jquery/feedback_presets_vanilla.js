/*
 * Vanilla JavaScript Feedback Preset Buttons for Classic SpeedGrader
 * Updated for stacked layout with emojis below
 */

(function () {
  "use strict";

  class FeedbackPresetsVanilla {
    constructor() {
      this.mountPoint = "#feedback-preset-buttons-container";
      this.textareaSelectors = [
        "#speed_grader_comment_textarea_mount_point textarea",
        'textarea[name="submission[comment]"]',
        "#add_a_comment textarea",
        ".comment-textarea",
      ];

      this.isEnabled = window.ENV && window.ENV.FEEDBACK_PRESETS_ENABLED;

      if (this.isEnabled) {
        this.init();
      }
    }

    init() {
      // Wait for DOM to be ready
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", () => this.render());
      } else {
        this.render();
      }

      // Also try after delays to ensure all DOM elements are loaded
      setTimeout(() => this.render(), 500);
      setTimeout(() => this.render(), 1500);
    }

    render() {
      const mountPoint = document.querySelector(this.mountPoint);
      if (!mountPoint) {
        // Try to find and create the structure
        this.ensureProperStructure();
        return;
      }

      const config = this.getConfig();
      const buttonsHTML = this.createButtonsHTML(
        config.presets,
        config.showEmojis
      );

      mountPoint.innerHTML = buttonsHTML;
      this.attachEventListeners();
      this.ensureProperLayout();
    }

    ensureProperStructure() {
      // Look for the ic-Action-header container
      const actionHeader = document.querySelector(
        "#add_a_comment .ic-Action-header"
      );
      if (!actionHeader) {
        return;
      }

      // Ensure action header has proper flex layout
      actionHeader.style.display = "flex";
      actionHeader.style.alignItems = "flex-start";
      actionHeader.style.gap = "12px";
      actionHeader.style.flexWrap = "nowrap";

      // Check if emojis-attachments-container exists
      let emojiContainer = actionHeader.querySelector(
        "#emojis-attachments-container"
      );
      if (!emojiContainer) {
        // Create the stacked container
        emojiContainer = document.createElement("div");
        emojiContainer.id = "emojis-attachments-container";
        emojiContainer.className = "emoji-attachments-stacked";
        emojiContainer.style.flex = "1";
        emojiContainer.style.order = "1";

        // Insert it before the secondary actions (Submit button)
        const secondary = actionHeader.querySelector(
          ".ic-Action-header__Secondary"
        );
        if (secondary) {
          actionHeader.insertBefore(emojiContainer, secondary);
          // Ensure secondary has proper styling
          secondary.style.order = "2";
          secondary.style.flexShrink = "0";
          secondary.style.alignSelf = "flex-start";
        } else {
          actionHeader.appendChild(emojiContainer);
        }
      }

      // Ensure it has the right class
      emojiContainer.className = "emoji-attachments-stacked";

      // Create feedback preset row if it doesn't exist
      let presetRow = emojiContainer.querySelector(".feedback-preset-row");
      if (!presetRow) {
        presetRow = document.createElement("div");
        presetRow.className = "feedback-preset-row";
        presetRow.style.display = "flex";
        presetRow.style.alignItems = "center";
        presetRow.style.gap = "8px";
        presetRow.style.justifyContent = "space-between";
        emojiContainer.appendChild(presetRow);
      }

      // Create preset container
      let presetContainer = presetRow.querySelector(
        "#feedback-preset-buttons-container"
      );
      if (!presetContainer) {
        presetContainer = document.createElement("div");
        presetContainer.id = "feedback-preset-buttons-container";
        presetContainer.className = "feedback-preset-container inline-presets";
        presetContainer.style.flex = "1";
        presetRow.appendChild(presetContainer);
      }

      // Move attach_things to the preset row if it's not there
      const attachThings = actionHeader.querySelector(".attach_things");
      if (attachThings) {
        if (!presetRow.contains(attachThings)) {
          presetRow.appendChild(attachThings);
        }
        // Ensure attach_things has proper styling
        attachThings.style.display = "flex";
        attachThings.style.alignItems = "center";
        attachThings.style.gap = "4px";
        attachThings.style.flexShrink = "0";
      }

      // Create emoji picker row if it doesn't exist
      let emojiRow = emojiContainer.querySelector(".emoji-picker-row");
      if (!emojiRow) {
        emojiRow = document.createElement("div");
        emojiRow.className = "emoji-picker-row";
        emojiRow.style.display = "flex";
        emojiRow.style.alignItems = "center";
        emojiRow.style.gap = "4px";
        emojiContainer.appendChild(emojiRow);
      }

      // Move emoji picker to emoji row
      const emojiPicker = actionHeader.querySelector(
        "#emoji-quick-picker-container"
      );
      if (emojiPicker) {
        if (!emojiRow.contains(emojiPicker)) {
          emojiRow.appendChild(emojiPicker);
        }
      } else {
        // Create emoji picker if it doesn't exist
        const newEmojiPicker = document.createElement("span");
        newEmojiPicker.id = "emoji-quick-picker-container";
        newEmojiPicker.innerHTML = `
          <div class="emoji-quick-picker">
            <button type="button" data-emoji="👍" title="👍">👍</button>
            <button type="button" data-emoji="👏" title="👏">👏</button>
            <button type="button" data-emoji="😀" title="😀">😀</button>
          </div>
        `;
        emojiRow.appendChild(newEmojiPicker);
      }

      // Now render the presets
      const config = this.getConfig();
      const buttonsHTML = this.createButtonsHTML(
        config.presets,
        config.showEmojis
      );
      presetContainer.innerHTML = buttonsHTML;
      this.attachEventListeners();
      this.styleEmojiButtons();
      this.ensureSubmitButtonStyling();
    }

    ensureProperLayout() {
      // Make sure the layout is correct
      const container = document.querySelector("#emojis-attachments-container");
      if (container) {
        container.className = "emoji-attachments-stacked";
      }

      // Ensure individual emoji buttons have proper styling
      this.styleEmojiButtons();
    }

    styleEmojiButtons() {
      // Add styling to individual emoji buttons (👍👏😀)
      const emojiContainer = document.querySelector(
        "#emoji-quick-picker-container"
      );
      if (emojiContainer) {
        const emojiButtons = emojiContainer.querySelectorAll("button");
        emojiButtons.forEach((button) => {
          button.style.fontSize = "18px";
          button.style.padding = "4px 6px";
          button.style.border = "none";
          button.style.background = "transparent";
          button.style.cursor = "pointer";
          button.style.transition = "transform 0.1s ease-in-out";

          // Add hover effect
          button.addEventListener("mouseenter", () => {
            button.style.transform = "scale(1.2)";
          });
          button.addEventListener("mouseleave", () => {
            button.style.transform = "scale(1)";
          });

          // Add click handler for emoji insertion
          button.addEventListener("click", (e) => {
            const emoji = button.dataset.emoji || button.textContent.trim();
            this.insertTextIntoTextarea(emoji);
          });
        });
      }
    }

    ensureSubmitButtonStyling() {
      // Ensure Submit button has proper styling
      const submitButton = document.querySelector("#comment_submit_button");
      if (submitButton) {
        submitButton.style.background = "#00843d";
        submitButton.style.borderColor = "#00843d";
        submitButton.style.color = "white";
        submitButton.style.padding = "8px 16px";
        submitButton.style.borderRadius = "3px";
        submitButton.style.fontWeight = "500";
        submitButton.style.fontSize = "14px";

        // Add hover effect
        submitButton.addEventListener("mouseenter", () => {
          submitButton.style.background = "#00752e";
          submitButton.style.borderColor = "#00752e";
        });
        submitButton.addEventListener("mouseleave", () => {
          submitButton.style.background = "#00843d";
          submitButton.style.borderColor = "#00843d";
        });
      }

      // Ensure secondary container has proper styling
      const secondary = document.querySelector(
        "#add_a_comment .ic-Action-header__Secondary"
      );
      if (secondary) {
        secondary.style.order = "2";
        secondary.style.flexShrink = "0";
        secondary.style.alignSelf = "flex-start";
      }
    }

    getConfig() {
      const defaultPresets = [
        { text: "Keep it up!", emoji: "🌟" },
        { text: "Nice try", emoji: "👍" },
        { text: "Good job", emoji: "✅" },
        { text: "Konting push pa", emoji: "💪" },
      ];

      return {
        presets: window.ENV.FEEDBACK_PRESETS || defaultPresets,
        showEmojis: window.ENV.FEEDBACK_PRESETS_SHOW_EMOJIS !== false,
        style: window.ENV.FEEDBACK_PRESETS_STYLE || "buttons",
      };
    }

    createButtonsHTML(presets, showEmojis) {
      return presets
        .map((preset, index) => {
          const emoji = showEmojis
            ? `<span class="preset-emoji">${preset.emoji}</span>`
            : "";
          const text = `<span class="preset-text">${preset.text}</span>`;

          return `
          <button 
            class="feedback-preset-button" 
            data-preset-text="${preset.text}" 
            data-preset-emoji="${preset.emoji}"
            data-index="${index}"
            title="Insert: ${preset.text}"
            type="button"
          >
            ${emoji}${text}
          </button>
        `;
        })
        .join("");
    }

    attachEventListeners() {
      const buttons = document.querySelectorAll(".feedback-preset-button");
      buttons.forEach((button) => {
        button.addEventListener("click", (e) => this.handlePresetClick(e));
      });
    }

    handlePresetClick(event) {
      const button = event.currentTarget;
      const text = button.dataset.presetText;
      const emoji = button.dataset.presetEmoji;
      const showEmojis = window.ENV.FEEDBACK_PRESETS_SHOW_EMOJIS !== false;

      const feedbackText = showEmojis ? `${emoji} ${text}` : text;

      // Simple visual feedback
      button.style.transform = "scale(0.95)";
      setTimeout(() => {
        button.style.transform = "";
      }, 150);

      this.insertTextIntoTextarea(feedbackText);
      this.trackUsage(feedbackText);
    }

    insertTextIntoTextarea(text) {
      const textarea = this.findTextarea();
      if (!textarea) {
        return;
      }

      const start = textarea.selectionStart || 0;
      const end = textarea.selectionEnd || 0;
      const currentValue = textarea.value || "";

      // Add space before if there's existing text
      const prefix =
        currentValue && start > 0 && !currentValue[start - 1].match(/\s/)
          ? " "
          : "";
      const insertText = prefix + text;

      // Insert the text
      textarea.value =
        currentValue.substring(0, start) +
        insertText +
        currentValue.substring(end);

      // Set cursor position after inserted text
      const newPosition = start + insertText.length;
      textarea.setSelectionRange(newPosition, newPosition);

      // Focus the textarea
      textarea.focus();

      // Trigger events for Canvas to detect changes
      this.triggerTextareaEvents(textarea);
    }

    findTextarea() {
      for (const selector of this.textareaSelectors) {
        const textarea = document.querySelector(selector);
        if (textarea) {
          return textarea;
        }
      }
      return null;
    }

    triggerTextareaEvents(textarea) {
      // Create and dispatch events
      const inputEvent = new Event("input", { bubbles: true });
      const changeEvent = new Event("change", { bubbles: true });

      textarea.dispatchEvent(inputEvent);
      textarea.dispatchEvent(changeEvent);

      // jQuery events if available
      if (window.$ && window.$(textarea).trigger) {
        window.$(textarea).trigger("input").trigger("change");
      }
    }

    trackUsage(feedbackText) {
      if (window.analytics && typeof window.analytics.track === "function") {
        window.analytics.track("Feedback Preset Used", {
          preset_text: feedbackText,
          context: "speedgrader_classic",
          course_id: window.ENV.COURSE_ID,
          assignment_id: window.ENV.ASSIGNMENT_ID,
        });
      }
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", function () {
      window.feedbackPresets = new FeedbackPresetsVanilla();
    });
  } else {
    window.feedbackPresets = new FeedbackPresetsVanilla();
  }

  // Multiple initialization attempts with delays
  setTimeout(function () {
    if (!window.feedbackPresets) {
      window.feedbackPresets = new FeedbackPresetsVanilla();
    } else {
      window.feedbackPresets.render();
    }
  }, 1000);

  setTimeout(function () {
    if (window.feedbackPresets) {
      window.feedbackPresets.render();
    }
  }, 2000);

  // Try again when SpeedGrader loads
  if (window.SpeedGrader) {
    const originalInit = window.SpeedGrader.init;
    window.SpeedGrader.init = function () {
      if (originalInit) originalInit.apply(this, arguments);
      setTimeout(() => {
        if (window.feedbackPresets) {
          window.feedbackPresets.render();
        }
      }, 500);
    };
  }
})();
