=> 
[32m#<Course:0x00007fbdc96990b8[0m
 id: [34m[1m0[0m,
 name: [31m[1m"[0m[31mUnnamed Course[0m[31m[1m"[0m,
 account_id: [34m[1m0[0m,
 group_weighting_scheme: [36m[1mnil[0m,
 workflow_state: [31m[1m"[0m[31mdeleted[0m[31m[1m"[0m,
 uuid: [31m[1m"[0m[31m5nkQnZdAVmlyWOjXLhZ8QYZLO9nb0eN65NJWDfYT[0m[31m[1m"[0m,
 start_at: [36m[1mnil[0m,
 conclude_at: [36m[1mnil[0m,
 grading_standard_id: [36m[1mnil[0m,
 is_public: [36m[1mnil[0m,
 allow_student_wiki_edits: [36m[1mfalse[0m,
 created_at: Mon, 02 Jun 2025 20:49:23.********* UTC +00:00,
 updated_at: Mon, 02 Jun 2025 20:49:23.********* UTC +00:00,
 show_public_context_messages: [36m[1mnil[0m,
 syllabus_body:
  [31m[1m"[0m[31m<h2>COURSE INFORMATION SYLLABUS (CIS)</h2>\n\n<table border=\"1\" cellpadding=\"8\" cellspacing=\"0\" style=\"border-collapse: collapse; width: 100%;\">\n  <tr>\n    <th style=\"text-align: left;\">Course Title</th>\n    <td>Unnamed Course</td>\n  </tr>\n  <tr>\n    <th style=\"text-align: left;\">Course Code</th>\n    <td>Unnamed</td>\n  </tr>\n  <tr>\n    <th style=\"text-align: left;\">Instructor</th>\n    <td>TBA</td>\n  </tr>\n  <tr>\n    <th style=\"text-align: left;\">VISION</th>\n    <td></td>\n  </tr>\n  <tr>\n    <th style=\"text-align: left;\">MISSION</th>\n    <td></td>\n  </tr>\n  <tr>\n    <th style=\"text-align: left;\">Course Category</th>\n    <td></td>\n  </tr>\n  <tr>\n    <th style=\"text-align: left;\">Prerequisite(s)</th>\n    <td></td>\n  </tr>\n  <tr>\n    <th style=\"text-align: left;\">Semester/Year</th>\n    <td></td>\n  </tr>\n  <tr>\n    <th style=\"text-align: left;\">Credit Hours</th>\n    <td></td>\n  </tr>\n  <tr>\n    <th style=\"text-align: left;\">Period of Study</th>\n    <td></td>\n  </tr>\n  <tr>\n    <th style=\"text-align: left;\">Course Rationale and Description</th>\n    <td></td>\n  </tr>\n  <tr>\n    <th style=\"text-align: left;\">Assessment Task</th>\n    <td></td>\n  </tr>\n</table>\n\n<h3>Intended Learning Outcomes (ILO)</h3>\n<ul>\n  <li>ILO1</li>\n  <li>ILO2</li>\n  <li>ILO3</li>\n  <li>ILO4</li>\n  <li>ILO5</li>\n</ul>\n\n<h2>COURSE POLICIES</h2>\n<p><strong>A. GRADING SYSTEM</strong></p>\n<p>The grading system adopted by this course is as follows:</p>\n\n<table border=\"1\" cellpadding=\"5\">\n  <tr><th>Rating</th><th>GPA</th><th>Score Range</th></tr>\n  <tr><td>Excellent</td><td>1.00</td><td>98 - 100</td></tr>\n  <tr><td>Superior</td><td>1.25</td><td>94 - 97</td></tr>\n  <tr><td>Very Good</td><td>1.50</td><td>90 - 93</td></tr>\n  <tr><td>Good</td><td>1.75</td><td>88 - 89</td></tr>\n  <tr><td>Meritorious</td><td>2.00</td><td>85 - 87</td></tr>\n  <tr><td>Very Satisfactory</td><td>2.25</td><td>83 - 84</td></tr>\n  <tr><td>Satisfactory</td><td>2.50</td><td>80 - 82</td></tr>\n  <tr><td>Fairly Satisfactory</td><td>2.75</td><td>78 - 79</td></tr>\n  <tr><td>Passing</td><td>3.00</td><td>75 - 77</td></tr>\n  <tr><td>Failure</td><td>5.00</td><td>Below 70</td></tr>\n  <tr><td>Incomplete</td><td>INC</td><td>70 - 74*</td></tr>\n</table>\n\n<p>*Students who got a computed grade of 70–74 will be given an appropriate remedial activity.</p>\n\n<h3>C. OTHER COURSE POLICIES AND REQUIREMENTS</h3>\n<p><strong>Teaching, Learning, and Assessment (TLA) Activities</strong></p>\n\n<table border=\"1\" cellpadding=\"5\">\n  <tr>\n    <th>Ch.</th><th>Topics / Reading List</th><th>Wks</th><th>Topic Outcomes</th><th>ILO</th><th>SO</th><th>Delivery Method</th>\n  </tr>\n    <tr>\n      <td>1</td><td></td><td></td><td></td><td></td><td></td><td></td>\n    </tr>\n    <tr>\n      <td>2</td><td></td><td></td><td></td><td></td><td></td><td></td>\n    </tr>\n    <tr>\n      <td>3</td><td></td><td></td><td></td><td></td><td></td><td></td>\n    </tr>\n    <tr>\n      <td>4</td><td></td><td></td><td></td><td></td><td></td><td></td>\n    </tr>\n</table>\n[0m[31m[1m"[0m,
 allow_student_forum_attachments: [36m[1mfalse[0m,
 default_wiki_editing_roles: [36m[1mnil[0m,
 wiki_id: [36m[1mnil[0m,
 allow_student_organized_groups: [36m[1mtrue[0m,
 course_code: [31m[1m"[0m[31mUnnamed[0m[31m[1m"[0m,
 default_view: [31m[1m"[0m[31mmodules[0m[31m[1m"[0m,
 abstract_course_id: [36m[1mnil[0m,
 root_account_id: [34m[1m0[0m,
 enrollment_term_id: [34m[1m0[0m,
 sis_source_id: [36m[1mnil[0m,
 sis_batch_id: [36m[1mnil[0m,
 open_enrollment: [36m[1mnil[0m,
 storage_quota: [36m[1mnil[0m,
 tab_configuration: [36m[1mnil[0m,
 allow_wiki_comments: [36m[1mnil[0m,
 turnitin_comments: [36m[1mnil[0m,
 self_enrollment: [36m[1mnil[0m,
 license: [36m[1mnil[0m,
 indexed: [36m[1mnil[0m,
 restrict_enrollments_to_course_dates: [36m[1mnil[0m,
 template_course_id: [36m[1mnil[0m,
 locale: [36m[1mnil[0m,
 settings: {},
 replacement_course_id: [36m[1mnil[0m,
 stuck_sis_fields: [31m[1m"[0m[31maccount_id,course_code,enrollment_term_id,name,workflow_state[0m[31m[1m"[0m,
 public_description: [36m[1mnil[0m,
 self_enrollment_code: [36m[1mnil[0m,
 self_enrollment_limit: [36m[1mnil[0m,
 integration_id: [36m[1mnil[0m,
 time_zone: [36m[1mnil[0m,
 lti_context_id: [36m[1mnil[0m,
 turnitin_id: [36m[1mnil[0m,
 show_announcements_on_home_page: [36m[1mnil[0m,
 home_page_announcement_limit: [36m[1mnil[0m,
 latest_outcome_import_id: [36m[1mnil[0m,
 grade_passback_setting: [36m[1mnil[0m,
 template: [36m[1mfalse[0m,
 homeroom_course: [36m[1mfalse[0m,
 sync_enrollments_from_homeroom: [36m[1mfalse[0m,
 homeroom_course_id: [36m[1mnil[0m,
 deleted_at: [36m[1mnil[0m,
 units: [36m[1mnil[0m,
 department: [36m[1mnil[0m,
 prerequisite: [36m[1mnil[0m,
 class_scheduler: [36m[1mnil[0m,
 class_time: [36m[1mnil[0m,
 class_location: [36m[1mnil[0m,
 archived_at: [36m[1mnil[0m,
 horizon_course: [36m[1mfalse[0m,
 department_id: [36m[1mnil[0m,
 restored_at: [36m[1mnil[0m,
 restored_by: [36m[1mnil[0m,
 restored: [36m[1mfalse[0m[32m>[0m
